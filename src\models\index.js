const Sequelize = require('sequelize');
const config = require('../config/config');
const db = {};

let sequelize;
if (process.env.NODE_ENV === 'production') {
  // Production ortamında .env dosyasındaki veritabanı bilgilerini kullan
  console.log('Veritabanı bağlantısı kuruluyor...');
  console.log('DB_HOST:', process.env.DB_HOST);
  console.log('DB_PORT:', process.env.DB_PORT);
  console.log('DB_NAME:', process.env.DB_NAME);
  console.log('DB_USER:', process.env.DB_USER);

  try {
    // Önce DATABASE_URL ile dene
    if (process.env.DATABASE_URL) {
      console.log('DATABASE_URL kullanılıyor:', process.env.DATABASE_URL);
      sequelize = new Sequelize(process.env.DATABASE_URL, {
        dialect: 'mysql',
        logging: false,
        pool: {
          max: 5,
          min: 0,
          acquire: 30000,
          idle: 10000
        },
        timezone: '+03:00'
      });
    } else {
      // Ayrı parametrelerle dene
      console.log('Ayrı parametreler kullanılıyor');
      sequelize = new Sequelize(
        process.env.DB_NAME,
        process.env.DB_USER,
        process.env.DB_PASSWORD,
        {
          host: process.env.DB_HOST,
          port: process.env.DB_PORT || 3306,
          dialect: 'mysql',
          logging: false,
          pool: {
            max: 5,
            min: 0,
            acquire: 30000,
            idle: 10000
          },
          timezone: '+03:00'
        }
      );
    }
  } catch (error) {
    console.error('Sequelize oluşturma hatası:', error);
    throw error;
  }
} else {
  // Development ortamında config dosyasındaki veritabanı bilgilerini kullan
  sequelize = new Sequelize(
    config.db.database,
    config.db.user,
    config.db.password,
    {
      host: config.db.host,
      dialect: 'mysql',
      logging: console.log,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      timezone: '+03:00'
    }
  );
}

// Model definitions
const User = require('./user')(sequelize, Sequelize);
const Permission = require('./permission')(sequelize, Sequelize);
const Agency = require('./agency')(sequelize, Sequelize);
const Carrier = require('./carrier')(sequelize, Sequelize);
const Region = require('./region')(sequelize, Sequelize);
// Hotel modeli kaldırıldı
const VehicleType = require('./vehicleType')(sequelize, Sequelize);
const Vehicle = require('./vehicle')(sequelize, Sequelize);
const Price = require('./price')(sequelize, Sequelize);
const Reservation = require('./reservation')(sequelize, Sequelize);
const Passenger = require('./passenger')(sequelize, Sequelize);
const Transfer = require('./transfer')(sequelize, Sequelize);
const ContactDetail = require('./contactDetail')(sequelize, Sequelize);
const ReservationLog = require('./reservationLog')(sequelize, Sequelize);
const Account = require('./account')(sequelize, Sequelize);
const Transaction = require('./transaction')(sequelize, Sequelize);
const Document = require('./document')(sequelize, Sequelize);
const ExchangeRate = require('./exchangeRate')(sequelize, Sequelize);

// Associations
// Users & Permissions
User.hasOne(Permission, { foreignKey: 'user_id', as: 'permissions' });
Permission.belongsTo(User, { foreignKey: 'user_id' });

// Regions Hierarchy

Region.hasMany(Region, { foreignKey: 'parent_region_id', as: 'subRegions' });
Region.belongsTo(Region, { foreignKey: 'parent_region_id', as: 'parentRegion' });

// Vehicle & Vehicle Types
VehicleType.hasMany(Vehicle, { foreignKey: 'vehicle_type_id', as: 'vehicles' });
Vehicle.belongsTo(VehicleType, { foreignKey: 'vehicle_type_id', as: 'vehicleType' });

// Carrier & Vehicles
Carrier.hasMany(Vehicle, { foreignKey: 'carrier_id', as: 'vehicles' });
Vehicle.belongsTo(Carrier, { foreignKey: 'carrier_id', as: 'carrier' });

// Prices
Agency.hasMany(Price, { foreignKey: 'agency_id' });
Price.belongsTo(Agency, { foreignKey: 'agency_id' });

Carrier.hasMany(Price, { foreignKey: 'carrier_id' });
Price.belongsTo(Carrier, { foreignKey: 'carrier_id' });

Region.hasMany(Price, { as: 'fromPrices', foreignKey: 'from_region_id' });
Price.belongsTo(Region, { as: 'fromRegion', foreignKey: 'from_region_id' });

Region.hasMany(Price, { as: 'toPrices', foreignKey: 'to_region_id' });
Price.belongsTo(Region, { as: 'toRegion', foreignKey: 'to_region_id' });

VehicleType.hasMany(Price, { foreignKey: 'vehicle_type_id' });
Price.belongsTo(VehicleType, { foreignKey: 'vehicle_type_id' });

// Reservations
Agency.hasMany(Reservation, { foreignKey: 'agency_id', as: 'reservations' });
Reservation.belongsTo(Agency, { foreignKey: 'agency_id', as: 'agency' });

Region.hasMany(Reservation, { as: 'fromReservations', foreignKey: 'from_region_id' });
Reservation.belongsTo(Region, { as: 'fromRegion', foreignKey: 'from_region_id' });

Region.hasMany(Reservation, { as: 'toReservations', foreignKey: 'to_region_id' });
Reservation.belongsTo(Region, { as: 'toRegion', foreignKey: 'to_region_id' });

// Hotel ilişkileri kaldırıldı - artık hotel_name ve secondary_hotel_name alanları kullanılıyor

VehicleType.hasMany(Reservation, { foreignKey: 'vehicle_type_id', as: 'reservations' });
Reservation.belongsTo(VehicleType, { foreignKey: 'vehicle_type_id', as: 'vehicleType' });

User.hasMany(Reservation, { foreignKey: 'created_by' });
Reservation.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

// Passengers
Reservation.hasMany(Passenger, { foreignKey: 'reservation_id', as: 'passengers' });
Passenger.belongsTo(Reservation, { foreignKey: 'reservation_id' });

// Transfers
Reservation.hasMany(Transfer, { foreignKey: 'reservation_id', as: 'transfers' });
Transfer.belongsTo(Reservation, { foreignKey: 'reservation_id', as: 'reservation' });

// Carrier & Transfers
Carrier.hasMany(Transfer, { foreignKey: 'carrier_id', as: 'transfers' });
Transfer.belongsTo(Carrier, { foreignKey: 'carrier_id', as: 'carrier' });

// Vehicle & Transfers
Vehicle.hasMany(Transfer, { foreignKey: 'vehicle_id', as: 'transfers' });
Transfer.belongsTo(Vehicle, { foreignKey: 'vehicle_id', as: 'vehicle' });

// Contact Details
Reservation.hasOne(ContactDetail, { foreignKey: 'reservation_id', as: 'contactDetails' });
ContactDetail.belongsTo(Reservation, { foreignKey: 'reservation_id' });

// Reservation Logs
Reservation.hasMany(ReservationLog, { foreignKey: 'reservation_id' });
ReservationLog.belongsTo(Reservation, { foreignKey: 'reservation_id' });

User.hasMany(ReservationLog, { foreignKey: 'user_id' });
ReservationLog.belongsTo(User, { foreignKey: 'user_id' });

// Transactions
Account.hasMany(Transaction, { foreignKey: 'account_id' });
Transaction.belongsTo(Account, { foreignKey: 'account_id' });

Reservation.hasMany(Transaction, { foreignKey: 'reservation_id' });
Transaction.belongsTo(Reservation, { foreignKey: 'reservation_id' });

User.hasMany(Transaction, { foreignKey: 'created_by' });
Transaction.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

// Documents
User.hasMany(Document, { foreignKey: 'uploaded_by' });
Document.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader' });

// Add models to db object
db.sequelize = sequelize;
db.Sequelize = Sequelize;
db.User = User;
db.Permission = Permission;
db.Agency = Agency;
db.Carrier = Carrier;
db.Region = Region;
// db.Hotel kaldırıldı
db.VehicleType = VehicleType;
db.Vehicle = Vehicle;
db.Price = Price;
db.Reservation = Reservation;
db.Passenger = Passenger;
db.Transfer = Transfer;
db.ContactDetail = ContactDetail;
db.ReservationLog = ReservationLog;
db.Account = Account;
db.Transaction = Transaction;
db.Document = Document;
db.ExchangeRate = ExchangeRate;

module.exports = db;