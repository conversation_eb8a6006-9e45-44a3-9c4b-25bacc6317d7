-- Hotel tablosunu kaldırdığım<PERSON>z için, hotel_id ve secondary_hotel_id sütunlarını kaldırıp
-- hotel_name ve secondary_hotel_name sütunlarını ekleyelim

-- Foreign key kısıtlamalarını devre dışı bırak
SET FOREIGN_KEY_CHECKS=0;

-- Önce foreign key kısıtlamalarını kaldırmaya çalış
-- (Hata verirse bu adımı atlayabilirsiniz, FOREIGN_KEY_CHECKS=0 sayesinde sorun olmaz)
ALTER TABLE reservations DROP FOREIGN KEY reservations_ibfk_4;

-- <PERSON><PERSON><PERSON> sütunları kaldırabiliriz
ALTER TABLE reservations DROP COLUMN hotel_id;
ALTER TABLE reservations DROP COLUMN secondary_hotel_id;

-- Şimdi hotel_name ve secondary_hotel_name sütunlarını ekleyelim
ALTER TABLE reservations ADD COLUMN hotel_name VARCHAR(100) NULL;
ALTER TABLE reservations ADD COLUMN secondary_hotel_name VARCHAR(100) NULL;

-- Agencies tablosundan agency_code sütununu kaldıralım
ALTER TABLE agencies DROP COLUMN agency_code;

-- Foreign key kısıtlamalarını tekrar etkinleştir
SET FOREIGN_KEY_CHECKS=1;
