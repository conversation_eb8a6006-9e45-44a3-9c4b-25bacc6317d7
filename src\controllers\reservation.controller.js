// server/controllers/reservation.controller.js

const reservationService = require('../services/reservation.service');

/**
 * Tüm rezervasyonları getir
 * @route GET /api/reservations
 */
exports.getAllReservations = async (req, res, next) => {
  try {
    const {
      status,
      agencyId,
      fromDate,
      toDate,
      searchTerm,
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortDir = 'DESC'
    } = req.query;

    const filters = {
      status,
      agencyId,
      fromDate,
      toDate,
      searchTerm,
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sortBy,
      sortDir
    };

    const result = await reservationService.getAllReservations(filters, req.user);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyon detaylarını getir
 * @route GET /api/reservations/:id
 */
exports.getReservationById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const reservation = await reservationService.getReservationById(id, req.user);

    res.status(200).json({
      success: true,
      reservation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni rezervasyon oluştur
 * @route POST /api/reservations
 */
exports.createReservation = async (req, res, next) => {
  try {
    const reservationData = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const reservation = await reservationService.createReservation(
      reservationData,
      userId,
      ipAddress
    );

    res.status(201).json({
      success: true,
      message: 'Rezervasyon başarıyla oluşturuldu',
      reservation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyon güncelleme
 * @route PUT /api/reservations/:id
 */
exports.updateReservation = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const reservation = await reservationService.updateReservation(
      id,
      updateData,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Rezervasyon başarıyla güncellendi',
      reservation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyon durumunu güncelleme
 * @route PATCH /api/reservations/:id/status
 */
exports.updateReservationStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const reservation = await reservationService.updateReservationStatus(
      id,
      status,
      notes,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Rezervasyon durumu başarıyla güncellendi',
      reservation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyon iptal etme
 * @route DELETE /api/reservations/:id
 */
exports.cancelReservation = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const reservation = await reservationService.cancelReservation(
      id,
      reason,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Rezervasyon başarıyla iptal edildi',
      reservation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyon detaylarını PDF olarak indirme (voucher)
 * @route GET /api/reservations/:id/voucher
 */
exports.generateReservationVoucher = async (req, res, next) => {
  try {
    const { id } = req.params;

    const voucherData = await reservationService.generateVoucher(id, req.user);

    res.status(200).json({
      success: true,
      message: 'Voucher şablonu oluşturuldu',
      voucherData
    });
  } catch (error) {
    next(error);
  }
};