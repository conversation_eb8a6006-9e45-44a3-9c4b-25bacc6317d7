// server/controllers/report.controller.js

const reportService = require('../services/report.service');

/**
 * Rezervasyon raporu
 * @route GET /api/reports/reservations
 */
exports.getReservationReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      agencyId, 
      status,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      agencyId,
      status,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    };
    
    const report = await reportService.getReservationReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Operasyon raporu
 * @route GET /api/reports/operations
 */
exports.getOperationReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      carrierId, 
      status,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      carrierId,
      status,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    };
    
    const report = await reportService.getOperationReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Finansal rapor
 * @route GET /api/reports/financial
 */
exports.getFinancialReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      entityType, 
      entityId,
      transactionType,
      currency
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      entityType,
      entityId,
      transactionType,
      currency
    };
    
    const report = await reportService.getFinancialReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente performans raporu
 * @route GET /api/reports/agencies
 */
exports.getAgencyReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      agencyId
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      agencyId
    };
    
    const report = await reportService.getAgencyReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacı performans raporu
 * @route GET /api/reports/carriers
 */
exports.getCarrierReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      carrierId
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      carrierId
    };
    
    const report = await reportService.getCarrierReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç kullanım raporu
 * @route GET /api/reports/vehicles
 */
exports.getVehicleReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      vehicleId,
      vehicleTypeId,
      carrierId
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      vehicleId,
      vehicleTypeId,
      carrierId
    };
    
    const report = await reportService.getVehicleReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölge bazlı rapor
 * @route GET /api/reports/regions
 */
exports.getRegionReport = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      regionId,
      category
    } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Başlangıç ve bitiş tarihleri gereklidir'
      });
    }
    
    const filters = {
      startDate,
      endDate,
      regionId,
      category
    };
    
    const report = await reportService.getRegionReport(filters);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Dashboard özet raporu
 * @route GET /api/reports/dashboard
 */
exports.getDashboardReport = async (req, res, next) => {
  try {
    const { period = 'today' } = req.query;
    
    const report = await reportService.getDashboardReport(period, req.user);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rapor dışa aktar (PDF/Excel)
 * @route POST /api/reports/export
 */
exports.exportReport = async (req, res, next) => {
  try {
    const { 
      reportType, 
      format = 'pdf', 
      filters 
    } = req.body;
    
    if (!reportType || !filters) {
      return res.status(400).json({
        success: false,
        message: 'Rapor tipi ve filtreler gereklidir'
      });
    }
    
    const result = await reportService.exportReport(reportType, format, filters);
    
    if (format === 'pdf') {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=${reportType}-report.pdf`);
      res.send(result);
    } else if (format === 'excel') {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=${reportType}-report.xlsx`);
      res.send(result);
    } else {
      res.status(200).json({
        success: true,
        data: result
      });
    }
  } catch (error) {
    next(error);
  }
};
