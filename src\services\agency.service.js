// server/services/agency.service.js

const {
    Agency,
    User,
    Price,
    Region,
    VehicleType,
    sequelize,
    Permission
  } = require('../models');
  const { Op } = require('sequelize');
  const bcrypt = require('bcryptjs');
  const AppError = require('../utils/appError');

  /**
   * Tüm acenteleri getirme servisi
   * @param {Object} filters - Filtreleme parametreleri
   * @returns {Object} Acente listesi ve sayfalama bilgileri
   */
  exports.getAllAgencies = async (filters) => {
    const {
      name,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortDir = 'ASC'
    } = filters;

    // Filtreleme koşulları
    const conditions = {};

    if (name) {
      conditions.name = { [Op.like]: `%${name}%` };
    }

    if (isActive !== undefined) {
      conditions.is_active = isActive;
    }

    // Sayısal değerleri kontrol et
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;

    // Sayfalama ve sıralama
    const offset = (pageNum - 1) * limitNum;
    const order = [[sortBy, sortDir]];

    const { count, rows: agencies } = await Agency.findAndCountAll({
      where: conditions,
      order,
      limit: limitNum,
      offset
    });

    // Sayfalama meta verisi
    const totalPages = Math.ceil(count / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrevious = pageNum > 1;

    return {
      agencies

      ,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNext,
      hasPrevious
    }
  };
};

/**
 * Acente detayı getirme servisi
 * @param {number} id - Acente ID
 * @returns {Object} Acente bilgileri
 */
exports.getAgencyById = async (id) => {
  const agency = await Agency.findByPk(id);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  return agency;
};

/**
 * Yeni acente oluşturma servisi
 * @param {Object} agencyData - Acente verileri
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Oluşturulan acente
 */
exports.createAgency = async (agencyData, userId) => {
  const {
    name,
    email,
    phone,
    address,
    contactPerson,
    // agencyCode kaldırıldı
    currency = 'EUR',
    isActive = true
  } = agencyData;

  // Acente kodu kontrolü kaldırıldı

  // Yeni acente oluştur
  const newAgency = await Agency.create({
    name,
    email,
    phone,
    address,
    contact_person: contactPerson,
    // agency_code alanı kaldırıldı
    currency,
    is_active: isActive
  });

  return newAgency;
};

/**
 * Acente güncelleme servisi
 * @param {number} id - Acente ID
 * @param {Object} updateData - Güncelleme verileri
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Güncellenmiş acente
 */
exports.updateAgency = async (id, updateData, userId) => {
  const agency = await Agency.findByPk(id);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  const {
    name,
    email,
    phone,
    address,
    contactPerson,
    currency
  } = updateData;

  // Güncellenecek alanları hazırla
  const fieldsToUpdate = {};
  if (name !== undefined) fieldsToUpdate.name = name;
  if (email !== undefined) fieldsToUpdate.email = email;
  if (phone !== undefined) fieldsToUpdate.phone = phone;
  if (address !== undefined) fieldsToUpdate.address = address;
  if (contactPerson !== undefined) fieldsToUpdate.contact_person = contactPerson;
  if (currency !== undefined) fieldsToUpdate.currency = currency;

  // Acenteyi güncelle
  await agency.update(fieldsToUpdate);

  return agency;
};

/**
 * Acente durumunu değiştirme servisi
 * @param {number} id - Acente ID
 * @param {boolean} isActive - Yeni durum
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Güncellenmiş acente
 */
exports.toggleAgencyStatus = async (id, isActive, userId) => {
  const agency = await Agency.findByPk(id);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  // Acenteyi güncelle
  await agency.update({ is_active: isActive });

  return agency;
};

/**
 * Acente fiyatları getirme servisi
 * @param {number} agencyId - Acente ID
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Fiyat listesi ve sayfalama bilgileri
 */
exports.getAgencyPrices = async (agencyId, filters) => {
  const {
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    page = 1,
    limit = 50
  } = filters;

  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  // Filtreleme koşulları
  const conditions = { agency_id: agencyId };

  if (fromRegionId) conditions.from_region_id = fromRegionId;
  if (toRegionId) conditions.to_region_id = toRegionId;
  if (vehicleTypeId) conditions.vehicle_type_id = vehicleTypeId;

  // Sayfalama ve sıralama
  const offset = (page - 1) * limit;
  const order = [
    ['from_region_id', 'ASC'],
    ['to_region_id', 'ASC'],
    ['vehicle_type_id', 'ASC']
  ];

  const { count, rows: prices } = await Price.findAndCountAll({
    where: conditions,
    include: [
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'vehicleType' }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama meta verisi
  const totalPages = Math.ceil(count / limit);
  const hasNext = page < totalPages;
  const hasPrevious = page > 1;

  return {
    prices,
    pagination: {
      total: count,
      totalPages,
      currentPage: parseInt(page),
      limit: parseInt(limit),
      hasNext,
      hasPrevious
    }
  };
};

/**
 * Acente fiyatı oluşturma servisi
 * @param {number} agencyId - Acente ID
 * @param {Object} priceData - Fiyat verileri
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Oluşturulan fiyat
 */
exports.createAgencyPrice = async (agencyId, priceData, userId) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  const {
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    price,
    currency = agency.currency,
    validFrom,
    validTo
  } = priceData;

  // Bölge ve araç tipi kontrolü
  const fromRegion = await Region.findByPk(fromRegionId);
  const toRegion = await Region.findByPk(toRegionId);
  const vehicleType = await VehicleType.findByPk(vehicleTypeId);

  if (!fromRegion) {
    throw new AppError('Başlangıç bölgesi bulunamadı', 404);
  }

  if (!toRegion) {
    throw new AppError('Varış bölgesi bulunamadı', 404);
  }

  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }

  // Aynı rota ve araç tipi için mevcut fiyat kontrolü
  const existingPrice = await Price.findOne({
    where: {
      agency_id: agencyId,
      from_region_id: fromRegionId,
      to_region_id: toRegionId,
      vehicle_type_id: vehicleTypeId,
      valid_from: validFrom
    }
  });

  if (existingPrice) {
    throw new AppError('Bu rota ve araç tipi için zaten bir fiyat tanımlanmış', 400);
  }

  // Yeni fiyat oluştur
  const newPrice = await Price.create({
    agency_id: agencyId,
    from_region_id: fromRegionId,
    to_region_id: toRegionId,
    vehicle_type_id: vehicleTypeId,
    price,
    currency,
    valid_from: validFrom,
    valid_to: validTo
  });

  // Oluşturulan fiyatı getir
  return Price.findByPk(newPrice.id, {
    include: [
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'vehicleType' }
    ]
  });
};

/**
 * Acente fiyatı güncelleme servisi
 * @param {number} agencyId - Acente ID
 * @param {number} priceId - Fiyat ID
 * @param {Object} updateData - Güncelleme verileri
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Güncellenmiş fiyat
 */
exports.updateAgencyPrice = async (agencyId, priceId, updateData, userId) => {
  // Fiyat kontrolü
  const price = await Price.findOne({
    where: {
      id: priceId,
      agency_id: agencyId
    }
  });

  if (!price) {
    throw new AppError('Fiyat bulunamadı', 404);
  }

  const {
    price: newPrice,
    currency,
    validFrom,
    validTo
  } = updateData;

  // Güncellenecek alanları hazırla
  const fieldsToUpdate = {};
  if (newPrice !== undefined) fieldsToUpdate.price = newPrice;
  if (currency !== undefined) fieldsToUpdate.currency = currency;
  if (validFrom !== undefined) fieldsToUpdate.valid_from = validFrom;
  if (validTo !== undefined) fieldsToUpdate.valid_to = validTo;

  // Fiyatı güncelle
  await price.update(fieldsToUpdate);

  // Güncellenmiş fiyatı getir
  return Price.findByPk(priceId, {
    include: [
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'vehicleType' }
    ]
  });
};

/**
 * Acente fiyatı silme servisi
 * @param {number} agencyId - Acente ID
 * @param {number} priceId - Fiyat ID
 * @param {number} userId - Kullanıcı ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteAgencyPrice = async (agencyId, priceId, userId) => {
  // Fiyat kontrolü
  const price = await Price.findOne({
    where: {
      id: priceId,
      agency_id: agencyId
    }
  });

  if (!price) {
    throw new AppError('Fiyat bulunamadı', 404);
  }

  // Fiyatı sil
  await price.destroy();

  return true;
};

/**
 * Acente kullanıcılarını getirme servisi
 * @param {number} agencyId - Acente ID
 * @returns {Array} Kullanıcı listesi
 */
exports.getAgencyUsers = async (agencyId) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  // Kullanıcıları getir
  const users = await User.findAll({
    where: {
      agency_id: agencyId,
      role: 'agent'
    },
    include: [{ model: Permission, as: 'permissions' }],
    attributes: { exclude: ['password'] }
  });

  return users;
};

/**
 * Acente kullanıcısı oluşturma servisi
 * @param {number} agencyId - Acente ID
 * @param {Object} userData - Kullanıcı verileri
 * @param {number} adminId - Admin kullanıcı ID
 * @returns {Object} Oluşturulan kullanıcı
 */
exports.createAgencyUser = async (agencyId, userData, adminId) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  const {
    username,
    password,
    email,
    fullName,
    phone,
    isActive = true,
    permissions = {}
  } = userData;

  // Kullanıcı adı veya e-posta kontrolü
  const existingUser = await User.findOne({
    where: {
      [Op.or]: [{ username }, { email }]
    }
  });

  if (existingUser) {
    throw new AppError('Bu kullanıcı adı veya e-posta zaten kullanılıyor', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();

  try {
    // Şifreyi hash'le
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Kullanıcı oluştur
    const newUser = await User.create({
      username,
      password: hashedPassword,
      email,
      full_name: fullName,
      phone,
      role: 'agent',
      agency_id: agencyId,
      is_active: isActive
    }, { transaction });

    // İzinleri oluştur
    await Permission.create({
      user_id: newUser.id,
      can_access_data: permissions.canAccessData || false,
      can_manage_prices: permissions.canManagePrices || false,
      can_manage_reservations: permissions.canManageReservations || true, // Varsayılan olarak rezervasyon yönetebilir
      can_manage_operations: permissions.canManageOperations || false,
      can_manage_payments: permissions.canManagePayments || false
    }, { transaction });

    await transaction.commit();

    // Oluşturulan kullanıcıyı getir (şifresiz)
    const createdUser = await User.findByPk(newUser.id, {
      include: [{ model: Permission, as: 'permissions' }],
      attributes: { exclude: ['password'] }
    });

    return createdUser;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Acente silme servisi
 * @param {number} id - Acente ID
 * @param {number} userId - Kullanıcı ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteAgency = async (id, userId) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(id);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  // Acente ile ilişkili rezervasyon kontrolü
  const reservationCount = await sequelize.models.Reservation.count({
    where: { agency_id: id }
  });

  if (reservationCount > 0) {
    throw new AppError('Bu acenteye ait rezervasyonlar bulunduğu için silinemez', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();

  try {
    // Acente kullanıcılarını getir
    const agencyUsers = await User.findAll({
      where: {
        agency_id: id,
        role: 'agent'
      }
    });

    // Kullanıcı izinlerini sil
    for (const user of agencyUsers) {
      await Permission.destroy({
        where: { user_id: user.id },
        transaction
      });
    }

    // Kullanıcıları sil
    await User.destroy({
      where: {
        agency_id: id,
        role: 'agent'
      },
      transaction
    });

    // Acente fiyatlarını sil
    await Price.destroy({
      where: { agency_id: id },
      transaction
    });

    // Acenteyi sil
    await agency.destroy({ transaction });

    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Acenteye ait rezervasyonları getirme servisi
 * @param {number} agencyId - Acente ID
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Rezervasyon listesi ve sayfalama bilgileri
 */
exports.getAgencyReservations = async (agencyId, filters) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  const {
    status,
    startDate,
    endDate,
    page = 1,
    limit = 20
  } = filters;

  // Filtreleme koşulları
  const conditions = { agency_id: agencyId };

  if (status) {
    conditions.status = status;
  }

  if (startDate && endDate) {
    conditions.created_at = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    conditions.created_at = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    conditions.created_at = {
      [Op.lte]: new Date(endDate)
    };
  }

  // Sayfalama ve sıralama
  const offset = (page - 1) * limit;
  const order = [['created_at', 'DESC']];

  const { count, rows: reservations } = await sequelize.models.Reservation.findAndCountAll({
    where: conditions,
    include: [
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' },
      {
        model: sequelize.models.Transfer,
        as: 'Transfers',
        include: [
          { model: sequelize.models.Vehicle, as: 'Vehicle' }
        ]
      }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama meta verisi
  const totalPages = Math.ceil(count / limit);
  const hasNext = page < totalPages;
  const hasPrevious = page > 1;

  return {
    reservations,
    pagination: {
      total: count,
      totalPages,
      currentPage: parseInt(page),
      limit: parseInt(limit),
      hasNext,
      hasPrevious
    }
  };
};

/**
 * Acente hesap bilgilerini getirme servisi
 * @param {number} agencyId - Acente ID
 * @param {string} currency - Para birimi (opsiyonel)
 * @returns {Object} Hesap bilgileri
 */
exports.getAgencyAccount = async (agencyId, currency) => {
  // Acente kontrolü
  const agency = await Agency.findByPk(agencyId);

  if (!agency) {
    throw new AppError('Acente bulunamadı', 404);
  }

  // Hesap bilgilerini getir
  const whereConditions = {
    entity_type: 'agency',
    entity_id: agencyId
  };

  if (currency) {
    whereConditions.currency = currency;
  }

  const account = await sequelize.models.Account.findOne({
    where: whereConditions
  });

  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }

  // Son işlemleri getir
  const transactions = await sequelize.models.Transaction.findAll({
    where: { account_id: account.id },
    include: [
      { model: sequelize.models.User, as: 'creator' },
      { model: sequelize.models.Reservation, as: 'Reservation' }
    ],
    order: [['transaction_date', 'DESC']],
    limit: 10
  });

  return {
    account,
    transactions
  };
};