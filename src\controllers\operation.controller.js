// server/controllers/operation.controller.js

const operationService = require('../services/operation.service');

/**
 * Günlük operasyon listesini getir
 * @route GET /api/operations/daily
 */
exports.getDailyOperations = async (req, res, next) => {
  try {
    const { 
      date, 
      startDate, 
      endDate, 
      transferType, 
      agencyId, 
      status, 
      page = 1, 
      limit = 50 
    } = req.query;

    const filters = {
      date,
      startDate,
      endDate,
      transferType,
      agencyId,
      status,
      page,
      limit
    };

    const result = await operationService.getDailyOperations(filters, req.user);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer durumunu güncelleme
 * @route PATCH /api/operations/transfers/:id/status
 */
exports.updateTransferStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;
    
    const transfer = await operationService.updateTransferStatus(
      id,
      status,
      notes,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Transfer durumu başarıyla güncellendi',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araca sürücü/araç atama
 * @route POST /api/operations/transfers/:id/assign-vehicle
 */
exports.assignVehicle = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { vehicleId } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;
    
    const transfer = await operationService.assignVehicle(
      id,
      vehicleId,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Araç başarıyla atandı',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Uçuş bilgilerini kontrol et
 * @route GET /api/operations/check-flight
 */
exports.checkFlightStatus = async (req, res, next) => {
  try {
    const { flightNumber, date } = req.query;
    
    const flightData = await operationService.checkFlightStatus(flightNumber, date);

    res.status(200).json({
      success: true,
      flight: flightData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer için WhatsApp mesajı gönder
 * @route POST /api/operations/transfers/:id/send-message
 */
exports.sendWhatsAppMessage = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { messageType } = req.body;
    const userId = req.user.id;
    
    const messageTemplate = await operationService.sendWhatsAppMessage(
      id,
      messageType,
      userId
    );

    res.status(200).json({
      success: true,
      message: 'WhatsApp mesajı başarıyla gönderildi',
      messageTemplate
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer detaylarını güncelleme
 * @route PUT /api/operations/transfers/:id
 */
exports.updateTransferDetails = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;
    
    const transfer = await operationService.updateTransferDetails(
      id,
      updateData,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Transfer detayları başarıyla güncellendi',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bugünkü transferlerin özeti
 * @route GET /api/operations/summary
 */
exports.getDailySummary = async (req, res, next) => {
  try {
    const { date } = req.query;
    
    const summary = await operationService.getDailySummary(date);

    res.status(200).json({
      success: true,
      summary
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç listesini getir (sürücü atama için)
 * @route GET /api/operations/available-vehicles
 */
exports.getAvailableVehicles = async (req, res, next) => {
  try {
    const { date, time, passengerCount, carrierId } = req.query;
    
    const vehicles = await operationService.getAvailableVehicles(
      date,
      time,
      passengerCount,
      carrierId
    );

    res.status(200).json({
      success: true,
      vehicles
    });
  } catch (error) {
    next(error);
  }
};