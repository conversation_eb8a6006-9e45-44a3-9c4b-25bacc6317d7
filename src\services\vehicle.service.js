// server/services/vehicle.service.js

const {
  Vehicle,
  VehicleType,
  Carrier,
  Transfer,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { getSafeFilename, isValidFileExtension } = require('../utils/helpers');

// Dosya yükleme dizini
const UPLOAD_DIR = path.join(__dirname, '../../uploads/vehicles');

// Dizin yoksa oluştur
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Tüm araçları listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç listesi ve sayfalama bilgileri
 */
exports.getAllVehicles = async (filters) => {
  const {
    plateNumber,
    carrierId,
    vehicleTypeId,
    isActive,
    page = 1,
    limit = 20,
    sortBy = 'plate_number',
    sortDir = 'ASC'
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {};

  if (plateNumber) {
    whereConditions.plate_number = { [Op.like]: `%${plateNumber}%` };
  }

  if (carrierId) {
    whereConditions.carrier_id = carrierId;
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  if (isActive !== undefined && isActive !== '') {
    whereConditions.is_active = isActive === 'true';
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Vehicle.findAndCountAll({
    where: whereConditions,
    include: [
      { model: VehicleType, as: 'vehicleType' },
      { model: Carrier, as: 'carrier' }
    ],
    order,
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Araç detaylarını getirme servisi
 * @param {number} id - Araç ID
 * @returns {Object} Araç detayları
 */
exports.getVehicleById = async (id) => {
  const vehicle = await Vehicle.findByPk(id, {
    include: [
      { model: VehicleType, as: 'vehicleType' },
      { model: Carrier, as: 'carrier' }
    ]
  });

  if (!vehicle) {
    throw new AppError('Araç bulunamadı', 404);
  }

  return vehicle;
};

/**
 * Yeni araç oluşturma servisi
 * @param {Object} vehicleData - Araç verileri
 * @returns {Object} Oluşturulan araç
 */
exports.createVehicle = async (vehicleData) => {
  const {
    plateNumber,
    vehicleTypeId,
    carrierId,
    driverName,
    driverPhone,
    isActive = true
  } = vehicleData;

  // Plaka kontrolü
  const existingVehicle = await Vehicle.findOne({
    where: { plate_number: plateNumber }
  });

  if (existingVehicle) {
    throw new AppError('Bu plaka ile bir araç zaten mevcut', 400);
  }

  // Araç tipi kontrolü
  const vehicleType = await VehicleType.findByPk(vehicleTypeId);
  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }

  // Taşımacı kontrolü
  const carrier = await Carrier.findByPk(carrierId);
  if (!carrier) {
    throw new AppError('Taşımacı bulunamadı', 404);
  }

  // Yeni araç oluştur
  const newVehicle = await Vehicle.create({
    plate_number: plateNumber,
    vehicle_type_id: vehicleTypeId,
    carrier_id: carrierId,
    driver_name: driverName,
    driver_phone: driverPhone,
    is_active: isActive,
    driver_rating: 0
  });

  return newVehicle;
};

/**
 * Araç güncelleme servisi
 * @param {number} id - Araç ID
 * @param {Object} vehicleData - Güncellenecek veriler
 * @returns {Object} Güncellenen araç
 */
exports.updateVehicle = async (id, vehicleData) => {
  const {
    plateNumber,
    vehicleTypeId,
    carrierId,
    driverName,
    driverPhone,
    isActive,
    driverRating
  } = vehicleData;

  // Aracı bul
  const vehicle = await Vehicle.findByPk(id);

  if (!vehicle) {
    throw new AppError('Araç bulunamadı', 404);
  }

  // Plaka kontrolü (eğer değiştiyse)
  if (plateNumber && plateNumber !== vehicle.plate_number) {
    const existingVehicle = await Vehicle.findOne({
      where: {
        plate_number: plateNumber,
        id: { [Op.ne]: id }
      }
    });

    if (existingVehicle) {
      throw new AppError('Bu plaka ile bir araç zaten mevcut', 400);
    }
  }

  // Araç tipi kontrolü (eğer değiştiyse)
  if (vehicleTypeId && vehicleTypeId !== vehicle.vehicle_type_id) {
    const vehicleType = await VehicleType.findByPk(vehicleTypeId);
    if (!vehicleType) {
      throw new AppError('Araç tipi bulunamadı', 404);
    }
  }

  // Taşımacı kontrolü (eğer değiştiyse)
  if (carrierId && carrierId !== vehicle.carrier_id) {
    const carrier = await Carrier.findByPk(carrierId);
    if (!carrier) {
      throw new AppError('Taşımacı bulunamadı', 404);
    }
  }

  // Güncelle
  await vehicle.update({
    plate_number: plateNumber || vehicle.plate_number,
    vehicle_type_id: vehicleTypeId || vehicle.vehicle_type_id,
    carrier_id: carrierId || vehicle.carrier_id,
    driver_name: driverName !== undefined ? driverName : vehicle.driver_name,
    driver_phone: driverPhone !== undefined ? driverPhone : vehicle.driver_phone,
    is_active: isActive !== undefined ? isActive : vehicle.is_active,
    driver_rating: driverRating !== undefined ? driverRating : vehicle.driver_rating
  });

  return vehicle;
};

/**
 * Araç silme servisi
 * @param {number} id - Araç ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteVehicle = async (id) => {
  // Aracı bul
  const vehicle = await Vehicle.findByPk(id);

  if (!vehicle) {
    throw new AppError('Araç bulunamadı', 404);
  }

  // Araca atanmış transfer var mı kontrol et
  const transferCount = await Transfer.count({
    where: { vehicle_id: id }
  });

  if (transferCount > 0) {
    throw new AppError('Bu araca atanmış transferler bulunduğu için silinemez', 400);
  }

  // Araç fotoğraflarını sil
  if (vehicle.vehicle_photo) {
    const vehiclePhotoPath = path.join(__dirname, '../../', vehicle.vehicle_photo);
    if (fs.existsSync(vehiclePhotoPath)) {
      fs.unlinkSync(vehiclePhotoPath);
    }
  }

  if (vehicle.driver_photo) {
    const driverPhotoPath = path.join(__dirname, '../../', vehicle.driver_photo);
    if (fs.existsSync(driverPhotoPath)) {
      fs.unlinkSync(driverPhotoPath);
    }
  }

  // Sil
  await vehicle.destroy();

  return true;
};

/**
 * Taşımacıya ait araçları getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç listesi ve sayfalama bilgileri
 */
exports.getVehiclesByCarrier = async (filters) => {
  const {
    carrierId,
    isActive,
    vehicleTypeId,
    page = 1,
    limit = 20
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {
    carrier_id: carrierId
  };

  if (isActive !== undefined && isActive !== '') {
    whereConditions.is_active = isActive === 'true';
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Veritabanı sorgusu
  const { count, rows } = await Vehicle.findAndCountAll({
    where: whereConditions,
    include: [
      { model: VehicleType, as: 'vehicleType' }
    ],
    order: [['plate_number', 'ASC']],
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Tipe göre araçları getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç listesi ve sayfalama bilgileri
 */
exports.getVehiclesByType = async (filters) => {
  const {
    vehicleTypeId,
    isActive,
    carrierId,
    page = 1,
    limit = 20
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {
    vehicle_type_id: vehicleTypeId
  };

  if (isActive !== undefined && isActive !== '') {
    whereConditions.is_active = isActive === 'true';
  }

  if (carrierId) {
    whereConditions.carrier_id = carrierId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Veritabanı sorgusu
  const { count, rows } = await Vehicle.findAndCountAll({
    where: whereConditions,
    include: [
      { model: Carrier, as: 'Carrier' }
    ],
    order: [['plate_number', 'ASC']],
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Araç fotoğrafı yükleme servisi
 * @param {number} id - Araç ID
 * @param {Object} file - Yüklenen dosya
 * @returns {Object} Güncellenen araç
 */
exports.uploadVehiclePhoto = async (id, file) => {
  // Aracı bul
  const vehicle = await Vehicle.findByPk(id);

  if (!vehicle) {
    throw new AppError('Araç bulunamadı', 404);
  }

  // Dosya uzantısı kontrolü
  if (!isValidFileExtension(file.originalname, ['.jpg', '.jpeg', '.png'])) {
    throw new AppError('Geçersiz dosya formatı. Sadece JPG, JPEG ve PNG dosyaları kabul edilir', 400);
  }

  // Eski fotoğrafı sil (eğer varsa)
  if (vehicle.vehicle_photo) {
    const oldPhotoPath = path.join(__dirname, '../../', vehicle.vehicle_photo);
    if (fs.existsSync(oldPhotoPath)) {
      fs.unlinkSync(oldPhotoPath);
    }
  }

  // Yeni fotoğrafı kaydet
  const fileName = getSafeFilename(file.originalname);
  const filePath = path.join(UPLOAD_DIR, fileName);

  fs.writeFileSync(filePath, file.buffer);

  // Veritabanını güncelle
  const relativePath = `uploads/vehicles/${fileName}`;
  await vehicle.update({
    vehicle_photo: relativePath
  });

  return {
    ...vehicle.toJSON(),
    vehicle_photo: relativePath
  };
};

/**
 * Sürücü fotoğrafı yükleme servisi
 * @param {number} id - Araç ID
 * @param {Object} file - Yüklenen dosya
 * @returns {Object} Güncellenen araç
 */
exports.uploadDriverPhoto = async (id, file) => {
  // Aracı bul
  const vehicle = await Vehicle.findByPk(id);

  if (!vehicle) {
    throw new AppError('Araç bulunamadı', 404);
  }

  // Dosya uzantısı kontrolü
  if (!isValidFileExtension(file.originalname, ['.jpg', '.jpeg', '.png'])) {
    throw new AppError('Geçersiz dosya formatı. Sadece JPG, JPEG ve PNG dosyaları kabul edilir', 400);
  }

  // Eski fotoğrafı sil (eğer varsa)
  if (vehicle.driver_photo) {
    const oldPhotoPath = path.join(__dirname, '../../', vehicle.driver_photo);
    if (fs.existsSync(oldPhotoPath)) {
      fs.unlinkSync(oldPhotoPath);
    }
  }

  // Yeni fotoğrafı kaydet
  const fileName = getSafeFilename(file.originalname);
  const filePath = path.join(UPLOAD_DIR, fileName);

  fs.writeFileSync(filePath, file.buffer);

  // Veritabanını güncelle
  const relativePath = `uploads/vehicles/${fileName}`;
  await vehicle.update({
    driver_photo: relativePath
  });

  return {
    ...vehicle.toJSON(),
    driver_photo: relativePath
  };
};

/**
 * Belirli tarih ve saatte müsait araçları getirme servisi
 * @param {Object} params - Parametre nesnesi
 * @returns {Array} Müsait araçlar listesi
 */
exports.getAvailableVehicles = async (params) => {
  const {
    date,
    time,
    vehicleTypeId,
    fromRegionId,
    toRegionId
  } = params;

  // Tarih ve saat bilgilerini birleştir
  const transferDateTime = new Date(`${date}T${time}`);

  // Tarih kontrolü
  if (isNaN(transferDateTime.getTime())) {
    throw new AppError('Geçersiz tarih veya saat formatı', 400);
  }

  // Filtreleme koşulları
  const whereConditions = {
    is_active: true
  };

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Tüm aktif araçları getir
  const allVehicles = await Vehicle.findAll({
    where: whereConditions,
    include: [
      { model: VehicleType, as: 'vehicleType' },
      { model: Carrier, as: 'carrier' }
    ],
    order: [['plate_number', 'ASC']]
  });

  // Belirtilen tarih ve saatte atanmış araçları bul
  const busyVehicleIds = await Transfer.findAll({
    attributes: ['vehicle_id'],
    where: {
      transfer_date: sequelize.literal(`DATE(transfer_date) = DATE('${date}')`),
      vehicle_id: { [Op.ne]: null },
      status: {
        [Op.notIn]: ['cancelled', 'no_show', 'failed']
      }
    },
    raw: true
  }).then(transfers => transfers.map(t => t.vehicle_id));

  // Müsait araçları filtrele
  const availableVehicles = allVehicles.filter(vehicle => !busyVehicleIds.includes(vehicle.id));

  return availableVehicles;
};
