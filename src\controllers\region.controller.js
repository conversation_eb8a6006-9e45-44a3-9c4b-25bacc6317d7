// server/controllers/region.controller.js

const regionService = require('../services/region.service');

/**
 * Tüm bölgeleri getir
 * @route GET /api/regions
 */
exports.getAllRegions = async (req, res, next) => {
  try {
    const {
      name,
      category,
      parentRegionId,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      name,
      category,
      parentRegionId,
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sortBy,
      sortDir
    };

    const result = await regionService.getAllRegions(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölge detaylarını getir
 * @route GET /api/regions/:id
 */
exports.getRegionById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const region = await regionService.getRegionById(id);

    res.status(200).json({
      success: true,
      data: region
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni bölge oluştur
 * @route POST /api/regions
 */
exports.createRegion = async (req, res, next) => {
  try {
    const {
      name,
      category,
      parentRegionId
    } = req.body;

    const newRegion = await regionService.createRegion({
      name,
      category,
      parentRegionId
    });

    res.status(201).json({
      success: true,
      data: newRegion,
      message: 'Bölge başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölge güncelle
 * @route PUT /api/regions/:id
 */
exports.updateRegion = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name,
      category,
      parentRegionId
    } = req.body;

    const updatedRegion = await regionService.updateRegion(id, {
      name,
      category,
      parentRegionId
    });

    res.status(200).json({
      success: true,
      data: updatedRegion,
      message: 'Bölge başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölge sil
 * @route DELETE /api/regions/:id
 */
exports.deleteRegion = async (req, res, next) => {
  try {
    const { id } = req.params;

    await regionService.deleteRegion(id);

    res.status(200).json({
      success: true,
      message: 'Bölge başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kategoriye göre bölgeleri getir
 * @route GET /api/regions/categories/:category
 */
exports.getRegionsByCategory = async (req, res, next) => {
  try {
    const { category } = req.params;

    const regions = await regionService.getRegionsByCategory(category);

    res.status(200).json({
      success: true,
      data: regions
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölgeye ait otelleri getir - Artık kullanılmıyor
 * @route GET /api/regions/:id/hotels
 */
exports.getRegionHotels = async (req, res, next) => {
  try {
    const { id } = req.params;

    const hotels = await regionService.getRegionHotels(id);

    res.status(200).json({
      success: true,
      data: hotels,
      message: 'Otel özelliği artık desteklenmiyor. Lütfen rezervasyon yaparken otel adını doğrudan girin.'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bölgeye otel ekle - Artık kullanılmıyor
 * @route POST /api/regions/:id/hotels
 */
exports.addHotelToRegion = async (req, res, next) => {
  try {
    // Artık desteklenmiyor, direkt hata dönüyöruz
    res.status(400).json({
      success: false,
      message: 'Otel ekleme özelliği artık desteklenmiyor. Lütfen rezervasyon yaparken otel adını doğrudan girin.'
    });
  } catch (error) {
    next(error);
  }
};
