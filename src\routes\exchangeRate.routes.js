// server/routes/exchangeRate.routes.js

const express = require('express');
const router = express.Router();
const exchangeRateController = require('../controllers/exchangeRate.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/exchange-rates
 * @desc Tüm döviz kurlarını getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  exchangeRateController.getAllExchangeRates
);

/**
 * @route GET /api/exchange-rates/current
 * @desc Güncel döviz kurlarını getir
 * @access Private
 */
router.get(
  '/current', 
  authenticate, 
  exchangeRateController.getCurrentRates
);

/**
 * @route GET /api/exchange-rates/:from/:to
 * @desc İki para birimi arasındaki kuru getir
 * @access Private
 */
router.get(
  '/:from/:to', 
  authenticate, 
  exchangeRateController.getRate
);

/**
 * @route POST /api/exchange-rates
 * @desc Yeni döviz kuru oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'finance']),
  exchangeRateController.createExchangeRate
);

/**
 * @route PUT /api/exchange-rates/:id
 * @desc Döviz kuru güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'finance']),
  exchangeRateController.updateExchangeRate
);

/**
 * @route DELETE /api/exchange-rates/:id
 * @desc Döviz kuru sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']),
  exchangeRateController.deleteExchangeRate
);

/**
 * @route POST /api/exchange-rates/sync
 * @desc Döviz kurlarını dış API'den senkronize et
 * @access Private
 */
router.post(
  '/sync', 
  authenticate, 
  authorize(['admin', 'finance']),
  exchangeRateController.syncExchangeRates
);

/**
 * @route POST /api/exchange-rates/convert
 * @desc Para birimi dönüşümü yap
 * @access Private
 */
router.post(
  '/convert', 
  authenticate, 
  exchangeRateController.convertCurrency
);

module.exports = router;
