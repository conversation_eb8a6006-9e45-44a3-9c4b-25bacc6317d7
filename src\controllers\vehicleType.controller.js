// server/controllers/vehicleType.controller.js

const vehicleTypeService = require('../services/vehicleType.service');

/**
 * Tüm araç tiplerini getir
 * @route GET /api/vehicle-types
 */
exports.getAllVehicleTypes = async (req, res, next) => {
  try {
    const { 
      name, 
      capacity,
      page = 1, 
      limit = 20,
      sortBy = 'name',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      name,
      capacity,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await vehicleTypeService.getAllVehicleTypes(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç tipi detaylarını getir
 * @route GET /api/vehicle-types/:id
 */
exports.getVehicleTypeById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const vehicleType = await vehicleTypeService.getVehicleTypeById(id);
    
    res.status(200).json({
      success: true,
      data: vehicleType
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni araç tipi oluştur
 * @route POST /api/vehicle-types
 */
exports.createVehicleType = async (req, res, next) => {
  try {
    const { name, capacity, description } = req.body;
    
    const newVehicleType = await vehicleTypeService.createVehicleType({
      name,
      capacity,
      description
    });
    
    res.status(201).json({
      success: true,
      data: newVehicleType,
      message: 'Araç tipi başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç tipi güncelle
 * @route PUT /api/vehicle-types/:id
 */
exports.updateVehicleType = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, capacity, description } = req.body;
    
    const updatedVehicleType = await vehicleTypeService.updateVehicleType(id, {
      name,
      capacity,
      description
    });
    
    res.status(200).json({
      success: true,
      data: updatedVehicleType,
      message: 'Araç tipi başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç tipi sil
 * @route DELETE /api/vehicle-types/:id
 */
exports.deleteVehicleType = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await vehicleTypeService.deleteVehicleType(id);
    
    res.status(200).json({
      success: true,
      message: 'Araç tipi başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};
