// server/controllers/auth.controller.js

const authService = require('../services/auth.service');

/**
 * <PERSON><PERSON>ı<PERSON> girişi
 * @route POST /api/auth/login
 */
exports.login = async (req, res, next) => {
  try {
    // CORS başlıklarını ekle
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

    console.log('Login isteği alındı:', req.body);

    const { username, password } = req.body;

    const result = await authService.login(username, password);

    res.status(200).json({
      success: true,
      token: result.token,
      user: result.user
    });
  } catch (error) {
    console.error('Login hatası:', error);
    next(error);
  }
};

/**
 * Mevcut kullanıcı bilgilerini getir
 * @route GET /api/auth/me
 */
exports.getCurrentUser = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const user = await authService.getCurrentUser(userId);

    res.status(200).json({
      success: true,
      user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Şifre değiştirme
 * @route POST /api/auth/change-password
 */
exports.changePassword = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    await authService.changePassword(userId, currentPassword, newPassword);

    res.status(200).json({
      success: true,
      message: 'Şifre başarıyla değiştirildi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni kullanıcı kaydı (sadece admin)
 * @route POST /api/auth/register
 */
exports.registerUser = async (req, res, next) => {
  try {
    const userData = {
      username: req.body.username,
      password: req.body.password,
      email: req.body.email,
      fullName: req.body.fullName,
      phone: req.body.phone,
      role: req.body.role,
      isActive: req.body.isActive,
      permissions: req.body.permissions || {}
    };

    const newUser = await authService.registerUser(userData, req.user.id);

    res.status(201).json({
      success: true,
      data: newUser,
      message: 'Kullanıcı başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Şifremi unuttum
 * @route POST /api/auth/forgot-password
 */
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    await authService.forgotPassword(email);

    res.status(200).json({
      success: true,
      message: 'Şifre sıfırlama talimatları e-posta adresinize gönderildi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Şifre sıfırlama
 * @route POST /api/auth/reset-password
 */
exports.resetPassword = async (req, res, next) => {
  try {
    const { token, newPassword } = req.body;

    await authService.resetPassword(token, newPassword);

    res.status(200).json({
      success: true,
      message: 'Şifreniz başarıyla sıfırlandı'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı çıkışı
 * @route POST /api/auth/logout
 */
exports.logout = async (req, res, next) => {
  try {
    // JWT tabanlı kimlik doğrulama kullanıldığı için,
    // sunucu tarafında özel bir işlem yapmaya gerek yok.
    // Client tarafında token'ı silmek yeterli olacaktır.

    res.status(200).json({
      success: true,
      message: 'Başarıyla çıkış yapıldı'
    });
  } catch (error) {
    next(error);
  }
};