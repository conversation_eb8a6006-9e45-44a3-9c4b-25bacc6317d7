// server/routes/account.routes.js

const express = require('express');
const router = express.Router();
const accountController = require('../controllers/account.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');

/**
 * @route GET /api/accounts
 * @desc Tüm hesapları getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  accountController.getAllAccounts
);

/**
 * @route GET /api/accounts/:id
 * @desc Hesap detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  accountController.getAccountById
);

/**
 * @route GET /api/accounts/entity/:type/:id
 * @desc Belirli bir varlığa (acente/taşımacı) ait hesabı getir
 * @access Private
 */
router.get(
  '/entity/:type/:id', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  accountController.getAccountByEntity
);

/**
 * @route POST /api/accounts
 * @desc Yeni hesap oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'finance']),
  accountController.createAccount
);

/**
 * @route PUT /api/accounts/:id
 * @desc Hesap güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'finance']),
  accountController.updateAccount
);

/**
 * @route GET /api/accounts/:id/transactions
 * @desc Hesap işlemlerini getir
 * @access Private
 */
router.get(
  '/:id/transactions', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  accountController.getAccountTransactions
);

/**
 * @route GET /api/accounts/:id/statement
 * @desc Hesap ekstresini getir
 * @access Private
 */
router.get(
  '/:id/statement', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  accountController.generateAccountStatement
);

module.exports = router;
