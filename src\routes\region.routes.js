// server/routes/region.routes.js

const express = require('express');
const router = express.Router();
const regionController = require('../controllers/region.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/regions
 * @desc Tüm bölgeleri getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  regionController.getAllRegions
);

/**
 * @route GET /api/regions/:id
 * @desc Bölge detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  regionController.getRegionById
);

/**
 * @route POST /api/regions
 * @desc Yeni bölge oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'manager']), 
  regionController.createRegion
);

/**
 * @route PUT /api/regions/:id
 * @desc Bölge güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']), 
  regionController.updateRegion
);

/**
 * @route DELETE /api/regions/:id
 * @desc Bölge sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']), 
  regionController.deleteRegion
);

/**
 * @route GET /api/regions/categories/:category
 * @desc Kategoriye göre bölgeleri getir
 * @access Private
 */
router.get(
  '/categories/:category', 
  authenticate, 
  regionController.getRegionsByCategory
);

/**
 * @route GET /api/regions/:id/hotels
 * @desc Bölgeye ait otelleri getir
 * @access Private
 */
router.get(
  '/:id/hotels', 
  authenticate, 
  regionController.getRegionHotels
);

/**
 * @route POST /api/regions/:id/hotels
 * @desc Bölgeye otel ekle
 * @access Private
 */
router.post(
  '/:id/hotels', 
  authenticate, 
  authorize(['admin', 'manager']), 
  regionController.addHotelToRegion
);

module.exports = router;
