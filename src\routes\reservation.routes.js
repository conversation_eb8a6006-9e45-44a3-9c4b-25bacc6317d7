// server/routes/reservation.routes.js

const express = require('express');
const router = express.Router();
const reservationController = require('../controllers/reservation.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');

/**
 * @route GET /api/reservations
 * @desc Tüm rezervasyonları getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  reservationController.getAllReservations
);

/**
 * @route GET /api/reservations/:id
 * @desc Rezervasyon detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  reservationController.getReservationById
);

/**
 * @route POST /api/reservations
 * @desc Yeni rezervasyon oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  reservationController.createReservation
);

/**
 * @route PUT /api/reservations/:id
 * @desc Rezervasyon güncelleme
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  reservationController.updateReservation
);

/**
 * @route PATCH /api/reservations/:id/status
 * @desc Rezervasyon durumunu güncelleme
 * @access Private
 */
router.patch(
  '/:id/status', 
  authenticate, 
  reservationController.updateReservationStatus
);

/**
 * @route DELETE /api/reservations/:id
 * @desc Rezervasyon iptal etme
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  reservationController.cancelReservation
);

/**
 * @route GET /api/reservations/:id/voucher
 * @desc Rezervasyon detaylarını PDF olarak indirme (voucher)
 * @access Private
 */
router.get(
  '/:id/voucher', 
  authenticate, 
  reservationController.generateReservationVoucher
);

module.exports = router;