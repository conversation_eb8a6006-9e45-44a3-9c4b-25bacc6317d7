module.exports = (sequelize, DataTypes) => {
  const Permission = sequelize.define('Permission', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    can_access_data: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    can_manage_prices: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    can_manage_reservations: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    can_manage_operations: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    can_manage_payments: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    tableName: 'permissions',
    timestamps: false
  });

  return Permission;
};