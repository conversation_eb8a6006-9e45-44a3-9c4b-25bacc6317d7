// server/services/operation.service.js

const { 
    Reservation, 
    Transfer, 
    Vehicle, 
    VehicleType,
    Region,
    Hotel,
    Passenger,
    ContactDetail,
    User,
    ReservationLog,
    Carrier,
    Agency,
    sequelize
  } = require('../models');
  const { Op } = require('sequelize');
  const AppError = require('../utils/appError');
  
  /**
   * Günlük operasyon listesini getirme servisi
   * @param {Object} filters - Filtreleme parametreleri
   * @param {Object} user - Kullanıcı bilgileri
   * @returns {Object} Transfer listesi ve sayfalama bilgileri
   */
  exports.getDailyOperations = async (filters, user) => {
    const { 
      date, 
      startDate, 
      endDate, 
      transferType, 
      agencyId, 
      status, 
      page = 1, 
      limit = 50 
    } = filters;
  
    // Filtreleme koşulları
    const conditions = {};
    
    // Tarih filtreleme
    if (date) {
      conditions.transfer_date = date;
    } else if (startDate && endDate) {
      conditions.transfer_date = {
        [Op.between]: [startDate, endDate]
      };
    } else {
      // Varsayılan olarak bugünün tarihi
      const today = new Date().toISOString().split('T')[0];
      conditions.transfer_date = today;
    }
  
    // Geliş/Gidiş filtreleme
    if (transferType === 'arrival') {
      conditions.is_return = false;
    } else if (transferType === 'departure') {
      conditions.is_return = true;
    }
  
    // Durum filtreleme
    if (status) {
      conditions.status = status;
    }
  
    // İlişkili modeller
    let include = [
      { 
        model: Reservation, 
        as: 'reservation',
        include: [
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' },
          { model: Hotel, as: 'hotel' },
          { model: Hotel, as: 'secondaryHotel' },
          { model: VehicleType, as: 'vehicleType' },
          { model: Passenger, as: 'passengers' },
          { model: ContactDetail, as: 'contactDetails' },
          { model: Agency, as: 'agency' }
        ]
      },
      { 
        model: Vehicle, 
        as: 'vehicle',
        include: [
          { model: VehicleType, as: 'vehicleType' },
          { model: Carrier, as: 'carrier' }
        ]
      }
    ];
  
    // Acente filtreleme (operasyon yetkilisi veya yönetici değilse)
    if (user.role !== 'admin' && user.role !== 'manager' && user.role !== 'operation') {
      if (user.role === 'agent') {
        if (user.agency_id) {
          include[0].where = { agency_id: user.agency_id };
        } else {
          throw new AppError('Bu işlem için yetkiniz yok', 403);
        }
      } else {
        throw new AppError('Bu işlem için yetkiniz yok', 403);
      }
    } else if (agencyId) {
      // Eğer yetkili kullanıcıysa ve agencyId belirtilmişse filtreye ekle
      include[0].where = { agency_id: agencyId };
    }
  
    // Sayfalama
    const offset = (page - 1) * limit;
  
    const { count, rows: transfers } = await Transfer.findAndCountAll({
      where: conditions,
      include,
      order: [
        ['transfer_date', 'ASC'],
        ['transfer_time', 'ASC']
      ],
      limit,
      offset,
      distinct: true
    });
  
    // Sayfalama meta verisi
    const totalPages = Math.ceil(count / limit);
    const hasNext = page < totalPages;
    const hasPrevious = page > 1;
  
    return {
      transfers,
      pagination: {
        total: count,
        totalPages,
        currentPage: parseInt(page),
        limit: parseInt(limit),
        hasNext,
        hasPrevious
      }
    };
  };
  
  /**
   * Transfer durumunu güncelleme servisi
   * @param {number} id - Transfer ID
   * @param {string} status - Yeni durum
   * @param {string} notes - Notlar
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Güncellenmiş transfer
   */
  exports.updateTransferStatus = async (id, status, notes, userId, ipAddress) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Durum kontrolü
      const validStatuses = ['pending', 'ready', 'in_progress', 'completed', 'cancelled', 'no_show', 'failed'];
      if (!validStatuses.includes(status)) {
        throw new AppError('Geçersiz durum değeri', 400);
      }
  
      // Transferi kontrol et
      const transfer = await Transfer.findByPk(id, {
        include: [{ model: Reservation, as: 'reservation' }]
      });
      
      if (!transfer) {
        throw new AppError('Transfer bulunamadı', 404);
      }
  
      // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
      const user = await User.findByPk(userId);
      if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
        throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
      }
  
      // Transfer durumunu güncelle
      await transfer.update({ status }, { transaction });
  
      // Eğer tüm transferler tamamlandıysa rezervasyon durumunu da güncelle
      if (status === 'completed' || status === 'no_show' || status === 'failed') {
        // Rezervasyona ait tüm transferleri kontrol et
        const allTransfers = await Transfer.findAll({
          where: { reservation_id: transfer.reservation_id }
        });
        
        // Tüm transferler tamamlandı mı kontrol et
        const allCompleted = allTransfers.every(t => 
          t.status === 'completed' || t.status === 'no_show' || t.status === 'failed'
        );
        
        if (allCompleted) {
          // Rezervasyon durumunu güncelle
          await transfer.reservation.update({ status: 'completed' }, { transaction });
        }
      }
  
      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: transfer.reservation_id,
        user_id: userId,
        action_type: 'transfer_status_update',
        action_details: `Transfer durumu değiştirildi: ${transfer.status} -> ${status}` + (notes ? ` (Not: ${notes})` : ''),
        ip_address: ipAddress
      }, { transaction });
  
      await transaction.commit();
  
      return {
        id: transfer.id,
        status: status
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };
  
  /**
   * Araç atama servisi
   * @param {number} id - Transfer ID
   * @param {number} vehicleId - Araç ID
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Güncellenmiş transfer
   */
  exports.assignVehicle = async (id, vehicleId, userId, ipAddress) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Transferi kontrol et
      const transfer = await Transfer.findByPk(id, {
        include: [
          { 
            model: Reservation, 
            as: 'reservation',
            include: [{ model: VehicleType, as: 'vehicleType' }]
          }
        ]
      });
      
      if (!transfer) {
        throw new AppError('Transfer bulunamadı', 404);
      }
  
      // Yetki kontrolü
      const user = await User.findByPk(userId);
      if (user.role !== 'admin' && user.role !== 'manager' && user.role !== 'operation') {
        throw new AppError('Bu işlem için yetkiniz yok', 403);
      }
  
      // Araç kontrolü
      const vehicle = await Vehicle.findByPk(vehicleId, {
        include: [{ model: VehicleType, as: 'vehicleType' }]
      });
      
      if (!vehicle) {
        throw new AppError('Araç bulunamadı', 404);
      }
  
      // Araç tipi uygunluk kontrolü
      if (vehicle.vehicleType.capacity < transfer.reservation.passenger_count) {
        throw new AppError(`Bu araç ${transfer.reservation.passenger_count} yolcu için yetersiz kapasiteye sahip`, 400);
      }
  
      // Aracı atama
      await transfer.update({ vehicle_id: vehicleId }, { transaction });
  
      // Transfer durumunu 'ready' olarak güncelle eğer 'pending' ise
      if (transfer.status === 'pending') {
        await transfer.update({ status: 'ready' }, { transaction });
      }
  
      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: transfer.reservation_id,
        user_id: userId,
        action_type: 'assign_vehicle',
        action_details: `Araç atandı: ${vehicle.plate_number} (${vehicle.vehicleType.name})`,
        ip_address: ipAddress
      }, { transaction });
  
      await transaction.commit();
  
      return {
        id: transfer.id,
        vehicle_id: vehicleId,
        status: transfer.status
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };
  
  /**
   * Uçuş bilgilerini kontrol etme servisi
   * @param {string} flightNumber - Uçuş numarası
   * @param {string} date - Tarih
   * @returns {Object} Uçuş bilgileri
   */
  exports.checkFlightStatus = async (flightNumber, date) => {
    if (!flightNumber) {
      throw new AppError('Uçuş numarası gerekli', 400);
    }
  
    try {
      // Burada FlightRadar24 veya benzer bir API'ye istek yapılacak
      // Örnek olarak simulasyon yapıyoruz
      
      // Gerçek uygulamada bu kısım FlightRadar24 API'sine bağlanacak
      // const response = await axios.get(`${config.flightRadarApi}/flight/${flightNumber}`, {
      //   params: { date },
      //   headers: { Authorization: `Bearer ${config.flightRadarApiKey}` }
      // });
      
      // return response.data;
      
      // Simülasyon verileri
      return {
        flightNumber,
        status: 'Scheduled',
        scheduledDeparture: new Date(),
        scheduledArrival: new Date(new Date().getTime() + 3 * 60 * 60 * 1000), // 3 saat sonra
        estimatedArrival: new Date(new Date().getTime() + 3 * 60 * 60 * 1000 + 15 * 60 * 1000), // 3 saat 15 dakika sonra
        delay: 15, // 15 dakika gecikme
        departureAirport: 'IST',
        arrivalAirport: 'AYT',
        aircraft: 'Airbus A320'
      };
    } catch (error) {
      throw new AppError('Uçuş bilgileri alınamadı', 500);
    }
  };
  
  /**
   * WhatsApp mesajı gönderme servisi
   * @param {number} id - Transfer ID
   * @param {string} messageType - Mesaj tipi (driver/customer)
   * @param {number} userId - Kullanıcı ID
   * @returns {string} Mesaj şablonu
   */
  exports.sendWhatsAppMessage = async (id, messageType, userId) => {
    // Transferi kontrol et
    const transfer = await Transfer.findByPk(id, {
      include: [
        { 
          model: Reservation, 
          as: 'reservation',
          include: [
            { model: Region, as: 'fromRegion' },
            { model: Region, as: 'toRegion' },
            { model: Hotel, as: 'hotel' },
            { model: ContactDetail, as: 'contactDetails' },
            { model: Passenger, as: 'passengers' }
          ]
        },
        { 
          model: Vehicle, 
          as: 'vehicle',
          include: [{ model: VehicleType, as: 'vehicleType' }]
        }
      ]
    });
    
    if (!transfer) {
      throw new AppError('Transfer bulunamadı', 404);
    }
  
    // Yetki kontrolü
    const user = await User.findByPk(userId);
    if (user.role !== 'admin' && user.role !== 'manager' && user.role !== 'operation') {
      throw new AppError('Bu işlem için yetkiniz yok', 403);
    }
  
    // Telefon numarası kontrolü
    const phone = transfer.reservation.contactDetails?.phone;
    if (!phone) {
      throw new AppError('Müşteri telefon numarası bulunamadı', 400);
    }
  
    // Mesaj şablonu hazırlama
    let messageTemplate;
    
    if (messageType === 'driver') {
      // Sürücüye gönderilecek mesaj şablonu
      messageTemplate = `*${transfer.is_return ? transfer.reservation.hotel.name : transfer.reservation.fromRegion.name} - ${transfer.is_return ? transfer.reservation.toRegion.name : transfer.reservation.hotel.name} Transferi*\n` +
        `${new Date(transfer.transfer_date).toLocaleDateString('tr-TR')} ${transfer.transfer_time}\n` +
        `${transfer.reservation.passengers[0]?.first_name} ${transfer.reservation.passengers[0]?.last_name}\n` +
        `${transfer.is_return ? 'terminal 2 bırakılır' : ''}\n` +
        `${transfer.reservation.passenger_count} PAX\n` +
        `${transfer.is_return ? transfer.reservation.hotel.name : ''}\n` +
        `${transfer.baby_seat_count > 0 ? `${transfer.baby_seat_count} adet Bebek koltuğu` : ''}`;
    } else {
      // Müşteriye gönderilecek mesaj şablonu
      messageTemplate = `Transfer bilgileriniz için:\n` +
        `Ad Soyad: ${transfer.reservation.passengers[0]?.first_name} ${transfer.reservation.passengers[0]?.last_name}\n` +
        `Tarih: ${new Date(transfer.transfer_date).toLocaleDateString('tr-TR')}\n` +
        `Saat: ${transfer.transfer_time}\n` +
        `Nereden: ${transfer.is_return ? transfer.reservation.hotel.name : transfer.reservation.fromRegion.name}\n` +
        `Nereye: ${transfer.is_return ? transfer.reservation.toRegion.name : transfer.reservation.hotel.name}\n` +
        `Kişi sayısı: ${transfer.reservation.passenger_count}\n` +
        `${transfer.vehicle ? `Araç: ${transfer.vehicle.vehicleType.name}` : ''}`;
    }
  
    // WhatsApp mesajı gönderme simulasyonu (gerçek implementasyonda WhatsApp Business API kullanılacak)
    // const whatsappApi = config.whatsappApi;
    // const whatsappApiKey = config.whatsappApiKey;
    // 
    // try {
    //   const response = await axios.post(`${whatsappApi}/send`, {
    //     phone,
    //     message: messageTemplate
    //   }, {
    //     headers: { Authorization: `Bearer ${whatsappApiKey}` }
    //   });
    //   
    //   return response.data;
    // } catch (error) {
    //   throw new AppError('WhatsApp mesajı gönderilemedi', 500);
    // }
  
    // Log kaydı oluştur
    await ReservationLog.create({
      reservation_id: transfer.reservation_id,
      user_id: userId,
      action_type: 'whatsapp_message',
      action_details: `WhatsApp mesajı gönderildi (${messageType === 'driver' ? 'sürücüye' : 'müşteriye'})`
    });
  
    return messageTemplate;
  };
  
  /**
   * Transfer detaylarını güncelleme servisi
   * @param {number} id - Transfer ID
   * @param {Object} updateData - Güncelleme verileri
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Güncellenmiş transfer
   */
  exports.updateTransferDetails = async (id, updateData, userId, ipAddress) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Transferi kontrol et
      const transfer = await Transfer.findByPk(id, {
        include: [{ model: Reservation, as: 'reservation' }]
      });
      
      if (!transfer) {
        throw new AppError('Transfer bulunamadı', 404);
      }
  
      // Yetki kontrolü
      const user = await User.findByPk(userId);
      if (user.role !== 'admin' && user.role !== 'manager' && user.role !== 'operation') {
        // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
        if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
          throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
        }
      }
  
      const {
        transferDate,
        transferTime,
        flightNumber,
        roomNumber,
        babySeatCount,
        childSeatCount,
        boosterCount,
        cashPayment,
        cashCurrency,
        driverNote,
        operationNote
      } = updateData;
  
      // Güncellenecek alanları hazırla
      const fieldsToUpdate = {};
      if (transferDate !== undefined) fieldsToUpdate.transfer_date = transferDate;
      if (transferTime !== undefined) fieldsToUpdate.transfer_time = transferTime;
      if (flightNumber !== undefined) fieldsToUpdate.flight_number = flightNumber;
      if (roomNumber !== undefined) fieldsToUpdate.room_number = roomNumber;
      if (babySeatCount !== undefined) fieldsToUpdate.baby_seat_count = babySeatCount;
      if (childSeatCount !== undefined) fieldsToUpdate.child_seat_count = childSeatCount;
      if (boosterCount !== undefined) fieldsToUpdate.booster_count = boosterCount;
      if (cashPayment !== undefined) fieldsToUpdate.cash_payment = cashPayment;
      if (cashCurrency !== undefined) fieldsToUpdate.cash_currency = cashCurrency;
      if (driverNote !== undefined) fieldsToUpdate.driver_note = driverNote;
      if (operationNote !== undefined) fieldsToUpdate.operation_note = operationNote;
  
      // Transferi güncelle
      await transfer.update(fieldsToUpdate, { transaction });
  
      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: transfer.reservation_id,
        user_id: userId,
        action_type: 'update_transfer',
        action_details: 'Transfer detayları güncellendi',
        ip_address: ipAddress
      }, { transaction });
  
      await transaction.commit();
  
      // Güncellenmiş transferi getir
      const updatedTransfer = await Transfer.findByPk(id, {
        include: [
          { 
            model: Reservation, 
            as: 'reservation',
            include: [
              { model: Region, as: 'fromRegion' },
              { model: Region, as: 'toRegion' },
              { model: Hotel, as: 'hotel' }
            ]
          },
          { model: Vehicle, as: 'vehicle' }
        ]
      });
  
      return updatedTransfer;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };
  
  /**
   * Günlük özet getirme servisi
   * @param {string} date - Tarih
   * @returns {Object} Günlük özet verileri
   */
  exports.getDailySummary = async (date) => {
    // Varsayılan olarak bugünün tarihi
    const targetDate = date || new Date().toISOString().split('T')[0];
    
    // Toplam transfer sayısı
    const totalTransfers = await Transfer.count({
      where: { transfer_date: targetDate }
    });
    
    // Durum bazında transfer sayıları
    const statusCounts = await Transfer.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { transfer_date: targetDate },
      group: ['status']
    });
    
    // Geliş ve gidiş bazında transfer sayıları
    const directionCounts = await Transfer.findAll({
      attributes: [
        'is_return',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { transfer_date: targetDate },
      group: ['is_return']
    });
    
    // Son eklenen transferler
    const recentTransfers = await Transfer.findAll({
      where: { transfer_date: targetDate },
      include: [
        { 
          model: Reservation, 
          as: 'reservation',
          include: [
            { model: Region, as: 'fromRegion' },
            { model: Region, as: 'toRegion' },
            { model: Hotel, as: 'hotel' }
          ]
        },
        { model: Vehicle, as: 'vehicle' }
      ],
      order: [['transfer_time', 'ASC']],
      limit: 5
    });
    
    // Özet verilerini formatlama
    const formattedStatusCounts = {};
    statusCounts.forEach(item => {
      formattedStatusCounts[item.status] = parseInt(item.getDataValue('count'));
    });
    
    const arrivalCount = directionCounts.find(item => !item.is_return)?.getDataValue('count') || 0;
    const departureCount = directionCounts.find(item => item.is_return)?.getDataValue('count') || 0;
    
    return {
      date: targetDate,
      totalTransfers,
      statusCounts: formattedStatusCounts,
      arrivalCount: parseInt(arrivalCount),
      departureCount: parseInt(departureCount),
      recentTransfers
    };
  };
  
  /**
   * Uygun araçları getirme servisi
   * @param {string} date - Tarih
   * @param {string} time - Saat
   * @param {number} passengerCount - Yolcu sayısı
   * @param {number} carrierId - Taşımacı ID (opsiyonel)
   * @returns {Array} Uygun araç listesi
   */
  exports.getAvailableVehicles = async (date, time, passengerCount, carrierId) => {
    if (!date || !time || !passengerCount) {
      throw new AppError('Tarih, saat ve yolcu sayısı gerekli', 400);
    }
  
    // Araç tipi filtreleme (minimum yolcu kapasitesine göre)
    const vehicleTypes = await VehicleType.findAll({
      where: {
        capacity: {
          [Op.gte]: passengerCount
        }
      }
    });
  
    const vehicleTypeIds = vehicleTypes.map(vt => vt.id);
    
    // Araç listesini getir
    const vehicles = await Vehicle.findAll({
      where: {
        vehicle_type_id: {
          [Op.in]: vehicleTypeIds
        },
        is_active: true,
        ...(carrierId && { carrier_id: carrierId })
      },
      include: [
        { model: VehicleType, as: 'vehicleType' },
        { model: Carrier, as: 'carrier' }
      ],
      order: [
        [sequelize.col('vehicleType.capacity'), 'ASC'],
        ['driver_rating', 'DESC']
      ]
    });
  
    // Belirtilen tarih ve saatte meşgul olan araçları bul
    const busyVehicleIds = await getBusyVehiclesAtTime(date, time);
    
    // Uygun araçları filtrele
    const availableVehicles = vehicles.filter(vehicle => !busyVehicleIds.includes(vehicle.id));
    
    return availableVehicles;
  };
  
  /**
   * Belirli bir tarih ve saatte meşgul olan araçları bul (yardımcı fonksiyon)
   * @param {string} date - Tarih
   * @param {string} time - Saat
   * @returns {Array} Meşgul araç ID'leri
   */
  async function getBusyVehiclesAtTime(date, time) {
    // Belirtilen saatten 2 saat önce ve 2 saat sonrası için zaman aralığı
    const timeAsDate = new Date(`${date}T${time}:00`);
    const twoHoursBefore = new Date(timeAsDate.getTime() - 2 * 60 * 60 * 1000).toTimeString().slice(0, 8);
    const twoHoursAfter = new Date(timeAsDate.getTime() + 2 * 60 * 60 * 1000).toTimeString().slice(0, 8);
    
    // Bu zaman aralığında transfer yapan araçları bul
    const busyTransfers = await Transfer.findAll({
      where: {
        transfer_date: date,
        transfer_time: {
          [Op.between]: [twoHoursBefore, twoHoursAfter]
        },
        vehicle_id: {
          [Op.not]: null
        },
        status: {
          [Op.notIn]: ['cancelled', 'completed', 'no_show', 'failed']
        }
      },
      attributes: ['vehicle_id']
    });
    
    return busyTransfers.map(transfer => transfer.vehicle_id);
  }