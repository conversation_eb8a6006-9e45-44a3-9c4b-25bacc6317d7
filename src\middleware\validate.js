// server/middleware/validate.js

/**
 * <PERSON><PERSON> ile validasyon middleware'i
 * @param {Object} schema - <PERSON><PERSON>
 * @returns {Function} Middleware fonksiyonu
 */
exports.validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (!error) {
      return next();
    }
    
    const errors = {};
    
    error.details.forEach((detail) => {
      const key = detail.path[0];
      errors[key] = detail.message;
    });
    
    return res.status(400).json({
      success: false,
      errors
    });
  };
};
