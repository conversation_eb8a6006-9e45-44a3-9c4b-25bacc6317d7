// server/routes/carrier.routes.js

const express = require('express');
const router = express.Router();
const carrierController = require('../controllers/carrier.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/carriers
 * @desc Tüm taşımacıları getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  carrierController.getAllCarriers
);

/**
 * @route GET /api/carriers/:id
 * @desc Taşımacı detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  carrierController.getCarrierById
);

/**
 * @route POST /api/carriers
 * @desc Yeni taşımacı oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'manager']), 
  carrierController.createCarrier
);

/**
 * @route PUT /api/carriers/:id
 * @desc Taşımacı güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']), 
  carrierController.updateCarrier
);

/**
 * @route DELETE /api/carriers/:id
 * @desc Taşımacı sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']), 
  carrierController.deleteCarrier
);

/**
 * @route GET /api/carriers/:id/vehicles
 * @desc Taşımacıya ait araçları getir
 * @access Private
 */
router.get(
  '/:id/vehicles', 
  authenticate, 
  carrierController.getCarrierVehicles
);

/**
 * @route GET /api/carriers/:id/account
 * @desc Taşımacı hesap bilgilerini getir
 * @access Private
 */
router.get(
  '/:id/account', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  carrierController.getCarrierAccount
);

module.exports = router;
