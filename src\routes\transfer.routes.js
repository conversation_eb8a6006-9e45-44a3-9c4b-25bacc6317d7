// server/routes/transfer.routes.js

const express = require('express');
const router = express.Router();
const transferController = require('../controllers/transfer.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');


/**
 * @route GET /api/transfers
 * @desc Tüm transferleri getir
 * @access Private
 */
router.get(
  '/',
  authenticate,
  authorize(['admin', 'manager', 'operation', 'agent']),
  transferController.getAllTransfers
);

/**
 * @route GET /api/transfers/:id
 * @desc Transfer detayını getir
 * @access Private
 */
router.get(
  '/:id',
  authenticate,
  authorize(['admin', 'manager', 'operation', 'agent']),
  transferController.getTransferById
);

/**
 * @route POST /api/transfers
 * @desc Yeni transfer oluştur
 * @access Private
 */
router.post(
  '/',
  authenticate,
  authorize(['admin', 'manager', 'operation']),
  transferController.createTransfer
);

/**
 * @route PUT /api/transfers/:id
 * @desc Transfer güncelle
 * @access Private
 */
router.put(
  '/:id',
  authenticate,
  authorize(['admin', 'manager', 'operation']),
  transferController.updateTransfer
);

/**
 * @route DELETE /api/transfers/:id
 * @desc Transfer sil
 * @access Private
 */
router.delete(
  '/:id',
  authenticate,
  authorize(['admin', 'manager']),
  transferController.deleteTransfer
);

/**
 * @route PATCH /api/transfers/:id/assign-carrier
 * @desc Transfere taşıyıcı firma ata
 * @access Private
 */
router.patch(
  '/:id/assign-carrier',
  authenticate,
  authorize(['admin', 'manager', 'operation']),
  transferController.assignCarrier
);

/**
 * @route PATCH /api/transfers/:id/assign-vehicle
 * @desc Transfere araç ata
 * @access Private
 */
router.patch(
  '/:id/assign-vehicle',
  authenticate,
  authorize(['admin', 'manager', 'operation']),
  transferController.assignVehicle
);

/**
 * @route PATCH /api/transfers/:id/status
 * @desc Transfer durumunu güncelle
 * @access Private
 */
router.patch(
  '/:id/status',
  authenticate,
  authorize(['admin', 'manager', 'operation']),
  transferController.updateStatus
);

module.exports = router;
