// server/routes/agency.routes.js

const express = require('express');
const router = express.Router();
const agencyController = require('../controllers/agency.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/agencies
 * @desc Tüm acenteleri getir
 * @access Private
 */
router.get(
  '/',
  authenticate,
  agencyController.getAllAgencies
);

/**
 * @route GET /api/agencies/:id
 * @desc Acente detaylarını getir
 * @access Private
 */
router.get(
  '/:id',
  authenticate,
  agencyController.getAgencyById
);

/**
 * @route POST /api/agencies
 * @desc Yeni acente oluştur
 * @access Private
 */
router.post(
  '/',
  authenticate,
  authorize(['admin', 'manager']),
  agencyController.createAgency
);

/**
 * @route PUT /api/agencies/:id
 * @desc Acente güncelle
 * @access Private
 */
router.put(
  '/:id',
  authenticate,
  authorize(['admin', 'manager']),
  agencyController.updateAgency
);

/**
 * @route DELETE /api/agencies/:id
 * @desc Acente sil
 * @access Private
 */
router.delete(
  '/:id',
  authenticate,
  authorize(['admin']),
  agencyController.deleteAgency
);

/**
 * @route GET /api/agencies/:id/reservations
 * @desc Acenteye ait rezervasyonları getir
 * @access Private
 */
router.get(
  '/:id/reservations',
  authenticate,
  agencyController.getAgencyReservations
);

/**
 * @route GET /api/agencies/:id/account
 * @desc Acente hesap bilgilerini getir
 * @access Private
 */
router.get(
  '/:id/account',
  authenticate,
  authorize(['admin', 'manager', 'finance']),
  agencyController.getAgencyAccount
);

/**
 * @route PATCH /api/agencies/:id/status
 * @desc Acente durumunu değiştir (aktif/pasif)
 * @access Private
 */
router.patch(
  '/:id/status',
  authenticate,
  authorize(['admin', 'manager']),
  agencyController.toggleAgencyStatus
);

module.exports = router;
