// server/services/auth.service.js

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { User, Permission, sequelize } = require('../models');
const { Op } = require('sequelize');
const config = require('../config/config');
const AppError = require('../utils/appError');
const emailService = require('./email.service');

/**
 * Kullanıcı girişi servisi
 * @param {string} username - Kullanıcı adı
 * @param {string} password - Kullanıcı şifresi
 * @returns {Object} token ve kullanıcı bilgileri
 */
exports.login = async (username, password) => {
  console.log('Auth service login çağrıldı:', { username });

  try {
    // Kullanıcıyı bul
    const user = await User.findOne({
      where: { username },
      include: [{ model: Permission, as: 'permissions' }]
    });

    if (!user) {
      console.log('Kullanıcı bulunamadı:', username);
      throw new AppError('Geçersiz kullanıcı adı veya şifre', 401);
    }

    // Şifreyi kontrol et
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      console.log('Şifre eşleşmedi:', username);
      throw new AppError('Geçersiz kullanıcı adı veya şifre', 401);
    }

    // Kullanıcı aktif değilse
    if (!user.is_active) {
      console.log('Kullanıcı aktif değil:', username);
      throw new AppError('Hesap devre dışı bırakılmış', 401);
    }

    console.log('Kullanıcı girişi başarılı:', username);

    // JWT token oluştur
    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        role: user.role
      },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Şifreyi çıkart
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    return {
      token,
      user: userWithoutPassword
    };
  } catch (error) {
    console.error('Login servisi hatası:', error);
    throw error;
  }
};

/**
 * Mevcut kullanıcıyı getirme servisi
 * @param {number} userId - Kullanıcı ID
 * @returns {Object} Kullanıcı bilgileri
 */
exports.getCurrentUser = async (userId) => {
  const user = await User.findByPk(userId, {
    include: [{ model: Permission, as: 'permissions' }],
    attributes: { exclude: ['password'] }
  });

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  return user;
};

/**
 * Şifre değiştirme servisi
 * @param {number} userId - Kullanıcı ID
 * @param {string} currentPassword - Mevcut şifre
 * @param {string} newPassword - Yeni şifre
 * @returns {boolean} İşlem başarılı mı
 */
exports.changePassword = async (userId, currentPassword, newPassword) => {
  // Kullanıcıyı bul
  const user = await User.findByPk(userId);
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Mevcut şifreyi kontrol et
  const isMatch = await bcrypt.compare(currentPassword, user.password);
  if (!isMatch) {
    throw new AppError('Mevcut şifre yanlış', 400);
  }

  // Yeni şifreyi hash'le ve güncelle
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  await user.update({ password: hashedPassword });

  return true;
};

/**
 * Kullanıcı kaydı servisi (admin tarafından)
 * @param {Object} userData - Kullanıcı verileri
 * @param {number} adminId - Admin kullanıcı ID
 * @returns {Object} Oluşturulan kullanıcı
 */
exports.registerUser = async (userData, adminId) => {
  const {
    username,
    password,
    email,
    fullName,
    phone,
    role,
    isActive = true,
    permissions = {}
  } = userData;

  // Admin kontrolü
  const admin = await User.findByPk(adminId);
  if (!admin || admin.role !== 'admin') {
    throw new AppError('Bu işlem için yetkiniz yok', 403);
  }

  // Kullanıcı adı veya e-posta kontrol et
  const existingUser = await User.findOne({
    where: {
      [Op.or]: [{ username }, { email }]
    }
  });

  if (existingUser) {
    throw new AppError('Bu kullanıcı adı veya e-posta zaten kullanılıyor', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();

  try {
    // Şifreyi hash'le
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Kullanıcı oluştur
    const newUser = await User.create({
      username,
      password: hashedPassword,
      email,
      full_name: fullName,
      phone,
      role,
      is_active: isActive
    }, { transaction });

    // İzinleri oluştur
    await Permission.create({
      user_id: newUser.id,
      can_access_data: permissions.canAccessData || false,
      can_manage_prices: permissions.canManagePrices || false,
      can_manage_reservations: permissions.canManageReservations || false,
      can_manage_operations: permissions.canManageOperations || false,
      can_manage_payments: permissions.canManagePayments || false
    }, { transaction });

    await transaction.commit();

    // Oluşturulan kullanıcıyı getir (şifresiz)
    const createdUser = await User.findByPk(newUser.id, {
      include: [{ model: Permission, as: 'permissions' }],
      attributes: { exclude: ['password'] }
    });

    return createdUser;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Şifremi unuttum servisi
 * @param {string} email - Kullanıcı e-posta adresi
 * @returns {boolean} İşlem başarılı mı
 */
exports.forgotPassword = async (email) => {
  // Kullanıcıyı bul
  const user = await User.findOne({ where: { email } });

  // Kullanıcı bulunamasa bile güvenlik için aynı mesajı döndür
  if (!user) {
    // Gerçek uygulamada e-posta gönderme işlemi yapılmaz
    return true;
  }

  // Şifre sıfırlama token'ı oluştur
  const resetToken = crypto.randomBytes(32).toString('hex');
  const resetTokenExpiry = Date.now() + 3600000; // 1 saat geçerli

  // Token'ı hash'le ve kullanıcıya kaydet
  const hashedToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  await user.update({
    reset_password_token: hashedToken,
    reset_password_expires: new Date(resetTokenExpiry)
  });

  // E-posta gönder
  try {
    const resetUrl = `${config.frontendUrl}/reset-password?token=${resetToken}`;

    await emailService.sendPasswordResetEmail(user.email, {
      name: user.full_name,
      resetUrl
    });

    return true;
  } catch (error) {
    // E-posta gönderme başarısız olursa token'ı sıfırla
    await user.update({
      reset_password_token: null,
      reset_password_expires: null
    });

    throw new AppError('E-posta gönderme hatası, lütfen daha sonra tekrar deneyin', 500);
  }
};

/**
 * Şifre sıfırlama servisi
 * @param {string} token - Şifre sıfırlama token'ı
 * @param {string} newPassword - Yeni şifre
 * @returns {boolean} İşlem başarılı mı
 */
exports.resetPassword = async (token, newPassword) => {
  // Token'ı hash'le
  const hashedToken = crypto
    .createHash('sha256')
    .update(token)
    .digest('hex');

  // Token'a sahip ve süresi geçmemiş kullanıcıyı bul
  const user = await User.findOne({
    where: {
      reset_password_token: hashedToken,
      reset_password_expires: { [Op.gt]: new Date() }
    }
  });

  if (!user) {
    throw new AppError('Geçersiz veya süresi dolmuş token', 400);
  }

  // Yeni şifreyi hash'le
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  // Şifreyi güncelle ve token'ı temizle
  await user.update({
    password: hashedPassword,
    reset_password_token: null,
    reset_password_expires: null
  });

  return true;
};