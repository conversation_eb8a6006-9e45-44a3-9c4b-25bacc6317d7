// CORS middleware - Tüm originlere izin ver
module.exports = (req, res, next) => {
  // Tüm originlere izin ver
  res.setHeader('Access-Control-Allow-Origin', '*');

  // Set other CORS headers
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Log request details for debugging
  console.log(`CORS Middleware: ${req.method} ${req.url}`);
  console.log(`Origin: ${req.headers.origin}`);

  next();
};
