module.exports = (sequelize, DataTypes) => {
  const Transfer = sequelize.define('Transfer', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    reservation_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'reservations',
        key: 'id'
      }
    },
    is_return: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    transfer_date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    transfer_time: {
      type: DataTypes.TIME,
      allowNull: false
    },
    flight_number: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    room_number: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    baby_seat_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    child_seat_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    booster_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    cash_payment: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    cash_currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'EUR'
    },
    driver_note: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    operation_note: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('pending', 'ready', 'in_progress', 'completed', 'cancelled', 'no_show', 'failed'),
      defaultValue: 'pending'
    },
    carrier_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'carriers',
        key: 'id'
      }
    },
    vehicle_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'vehicles',
        key: 'id'
      }
    }
  }, {
    tableName: 'transfers',
    timestamps: false
  });

  return Transfer;
};