// server/services/email.service.js

const nodemailer = require('nodemailer');
const config = require('../config/config');

// E-posta gönderici yapılandırması
let transporter;

// Geliştirme ortamında sahte transporter kullan
if (process.env.NODE_ENV === 'development') {
  transporter = {
    sendMail: async (mailOptions) => {
      console.log('E-posta gönderildi (DEV):', mailOptions);
      return { messageId: 'dev-message-id' };
    }
  };
} else {
  // Gerçek ortamda SMTP yapılandırması
  transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    }
  });
}

/**
 * <PERSON><PERSON><PERSON> sıfırlama e-postası gönderme
 * @param {string} to - Alıcı e-posta adresi
 * @param {Object} data - E-posta şablonu verileri
 * @returns {Promise} E-posta gönderim sonucu
 */
exports.sendPasswordResetEmail = async (to, data) => {
  const { name, resetUrl } = data;

  const mailOptions = {
    from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_EMAIL}>`,
    to,
    subject: 'Şifre Sıfırlama Talebi',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Şifre Sıfırlama</h2>
        <p>Merhaba ${name},</p>
        <p>Hesabınız için bir şifre sıfırlama talebinde bulundunuz. Şifrenizi sıfırlamak için aşağıdaki bağlantıya tıklayın:</p>
        <p>
          <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">
            Şifremi Sıfırla
          </a>
        </p>
        <p>Bu bağlantı 1 saat boyunca geçerlidir.</p>
        <p>Eğer şifre sıfırlama talebinde bulunmadıysanız, bu e-postayı görmezden gelebilirsiniz.</p>
        <p>Saygılarımızla,<br>CVR TOURISM & TRASNFER Ekibi</p>
      </div>
    `
  };

  return await transporter.sendMail(mailOptions);
};

/**
 * Hoş geldiniz e-postası gönderme
 * @param {string} to - Alıcı e-posta adresi
 * @param {Object} data - E-posta şablonu verileri
 * @returns {Promise} E-posta gönderim sonucu
 */
exports.sendWelcomeEmail = async (to, data) => {
  const { name, username, loginUrl } = data;

  const mailOptions = {
    from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_EMAIL}>`,
    to,
    subject: 'CVR TOURISM & TRASNFERne Hoş Geldiniz',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Hoş Geldiniz!</h2>
        <p>Merhaba ${name},</p>
        <p>CVR TOURISM & TRASNFERne kaydınız başarıyla tamamlandı. Aşağıdaki bilgilerle giriş yapabilirsiniz:</p>
        <p><strong>Kullanıcı Adı:</strong> ${username}</p>
        <p>
          <a href="${loginUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">
            Giriş Yap
          </a>
        </p>
        <p>Herhangi bir sorunuz olursa, lütfen bizimle iletişime geçin.</p>
        <p>Saygılarımızla,<br>CVR TOURISM & TRASNFER Ekibi</p>
      </div>
    `
  };

  return await transporter.sendMail(mailOptions);
};

/**
 * Rezervasyon onay e-postası gönderme
 * @param {string} to - Alıcı e-posta adresi
 * @param {Object} data - E-posta şablonu verileri
 * @returns {Promise} E-posta gönderim sonucu
 */
exports.sendReservationConfirmationEmail = async (to, data) => {
  const {
    name,
    reservationNumber,
    transferDate,
    transferTime,
    fromLocation,
    toLocation,
    passengerCount,
    vehicleType,
    viewUrl
  } = data;

  const mailOptions = {
    from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_EMAIL}>`,
    to,
    subject: `Rezervasyon Onayı: ${reservationNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Rezervasyon Onayı</h2>
        <p>Merhaba ${name},</p>
        <p>Rezervasyonunuz başarıyla oluşturuldu. Detaylar aşağıdadır:</p>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Rezervasyon Numarası:</strong> ${reservationNumber}</p>
          <p><strong>Transfer Tarihi:</strong> ${transferDate}</p>
          <p><strong>Transfer Saati:</strong> ${transferTime}</p>
          <p><strong>Nereden:</strong> ${fromLocation}</p>
          <p><strong>Nereye:</strong> ${toLocation}</p>
          <p><strong>Yolcu Sayısı:</strong> ${passengerCount}</p>
          <p><strong>Araç Tipi:</strong> ${vehicleType}</p>
        </div>

        <p>
          <a href="${viewUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">
            Rezervasyon Detaylarını Görüntüle
          </a>
        </p>

        <p>Herhangi bir sorunuz olursa, lütfen bizimle iletişime geçin.</p>
        <p>Saygılarımızla,<br>CVR TOURISM & TRASNFER Ekibi</p>
      </div>
    `
  };

  return await transporter.sendMail(mailOptions);
};
