// server/routes/report.routes.js

const express = require('express');
const router = express.Router();
const reportController = require('../controllers/report.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/reports/reservations
 * @desc Rezervasyon raporu
 * @access Private
 */
router.get(
  '/reservations', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  reportController.getReservationReport
);

/**
 * @route GET /api/reports/operations
 * @desc Operasyon raporu
 * @access Private
 */
router.get(
  '/operations', 
  authenticate, 
  authorize(['admin', 'manager', 'operation']),
  reportController.getOperationReport
);

/**
 * @route GET /api/reports/financial
 * @desc Finansal rapor
 * @access Private
 */
router.get(
  '/financial', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  reportController.getFinancialReport
);

/**
 * @route GET /api/reports/agencies
 * @desc Acente performans raporu
 * @access Private
 */
router.get(
  '/agencies', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  reportController.getAgencyReport
);

/**
 * @route GET /api/reports/carriers
 * @desc Taşımacı performans raporu
 * @access Private
 */
router.get(
  '/carriers', 
  authenticate, 
  authorize(['admin', 'manager', 'operation']),
  reportController.getCarrierReport
);

/**
 * @route GET /api/reports/vehicles
 * @desc Araç kullanım raporu
 * @access Private
 */
router.get(
  '/vehicles', 
  authenticate, 
  authorize(['admin', 'manager', 'operation']),
  reportController.getVehicleReport
);

/**
 * @route GET /api/reports/regions
 * @desc Bölge bazlı rapor
 * @access Private
 */
router.get(
  '/regions', 
  authenticate, 
  authorize(['admin', 'manager']),
  reportController.getRegionReport
);

/**
 * @route GET /api/reports/dashboard
 * @desc Dashboard özet raporu
 * @access Private
 */
router.get(
  '/dashboard', 
  authenticate, 
  reportController.getDashboardReport
);

/**
 * @route POST /api/reports/export
 * @desc Rapor dışa aktar (PDF/Excel)
 * @access Private
 */
router.post(
  '/export', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  reportController.exportReport
);

module.exports = router;
