// server/controllers/document.controller.js

const documentService = require('../services/document.service');

/**
 * Tüm belgeleri getir
 * @route GET /api/documents
 */
exports.getAllDocuments = async (req, res, next) => {
  try {
    const { 
      entityType, 
      entityId, 
      documentType,
      page = 1, 
      limit = 20,
      sortBy = 'upload_date',
      sortDir = 'DESC'
    } = req.query;

    const filters = {
      entityType,
      entityId,
      documentType,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await documentService.getAllDocuments(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belge detaylarını getir
 * @route GET /api/documents/:id
 */
exports.getDocumentById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const document = await documentService.getDocumentById(id);
    
    res.status(200).json({
      success: true,
      data: document
    });
  } catch (error) {
    next(error);
  }
};

/**
 * <PERSON><PERSON> belge yü<PERSON>
 * @route POST /api/documents
 */
exports.uploadDocument = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Lütfen bir dosya yükleyin'
      });
    }
    
    const { entityType, entityId, documentType } = req.body;
    
    const newDocument = await documentService.uploadDocument({
      entityType,
      entityId,
      documentType,
      file: req.file,
      uploadedBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: newDocument,
      message: 'Belge başarıyla yüklendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belge sil
 * @route DELETE /api/documents/:id
 */
exports.deleteDocument = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await documentService.deleteDocument(id);
    
    res.status(200).json({
      success: true,
      message: 'Belge başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belirli bir varlığa ait belgeleri getir
 * @route GET /api/documents/entity/:type/:id
 */
exports.getDocumentsByEntity = async (req, res, next) => {
  try {
    const { type, id } = req.params;
    const { documentType } = req.query;
    
    const documents = await documentService.getDocumentsByEntity(type, id, documentType);
    
    res.status(200).json({
      success: true,
      data: documents
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belge indir
 * @route GET /api/documents/download/:id
 */
exports.downloadDocument = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const document = await documentService.getDocumentForDownload(id);
    
    res.download(document.filePath, document.fileName, (err) => {
      if (err) {
        next(err);
      }
    });
  } catch (error) {
    next(error);
  }
};
