// server/routes/user.routes.js

const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/users
 * @desc Tüm kullanıcıları getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  authorize(['admin', 'manager']),
  userController.getAllUsers
);

/**
 * @route GET /api/users/:id
 * @desc Kullanıcı detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']),
  userController.getUserById
);

/**
 * @route POST /api/users
 * @desc Yeni kullanıcı oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin']),
  userController.createUser
);

/**
 * @route PUT /api/users/:id
 * @desc Kullanıcı güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin']),
  userController.updateUser
);

/**
 * @route DELETE /api/users/:id
 * @desc Kullanıcı sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']),
  userController.deleteUser
);

/**
 * @route PATCH /api/users/:id/status
 * @desc Kullanıcı durumunu güncelle (aktif/pasif)
 * @access Private
 */
router.patch(
  '/:id/status', 
  authenticate, 
  authorize(['admin']),
  userController.updateUserStatus
);

/**
 * @route PUT /api/users/:id/permissions
 * @desc Kullanıcı izinlerini güncelle
 * @access Private
 */
router.put(
  '/:id/permissions', 
  authenticate, 
  authorize(['admin']),
  userController.updateUserPermissions
);

/**
 * @route GET /api/users/role/:role
 * @desc Role göre kullanıcıları getir
 * @access Private
 */
router.get(
  '/role/:role', 
  authenticate, 
  authorize(['admin', 'manager']),
  userController.getUsersByRole
);

module.exports = router;
