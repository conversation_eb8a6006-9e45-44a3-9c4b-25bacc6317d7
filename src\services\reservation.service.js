// server/services/reservationService.js

const {
    Reservation,
    Agency,
    Region,
    // Hotel modeli kaldırıldı
    VehicleType,
    Transfer,
    Passenger,
    ContactDetail,
    ReservationLog,
    Vehicle,
    User,
    sequelize
  } = require('../models');
  const { Op } = require('sequelize');
  const AppError = require('../utils/appError');
  const helpers = require('../utils/helpers');

  /**
   * Tüm rezervasyonları listeleme servisi
   * @param {Object} filters - Filtreleme parametreleri
   * @param {Object} user - Kullanıcı bilgileri
   * @returns {Object} Rezervasyon listesi ve sayfalama bilgileri
   */
  exports.getAllReservations = async (filters, user) => {
    const {
      status,
      agencyId,
      fromDate,
      toDate,
      searchTerm,
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortDir = 'DESC'
    } = filters;

    // Filtreleme koşulları
    const conditions = {};

    if (status) conditions.status = status;
    if (agencyId) conditions.agency_id = agencyId;

    // <PERSON><PERSON>h a<PERSON>ığı filtreleme
    if (fromDate || toDate) {
      conditions.created_at = {};
      if (fromDate) conditions.created_at[Op.gte] = new Date(fromDate);
      if (toDate) {
        const endDate = new Date(toDate);
        endDate.setHours(23, 59, 59, 999);
        conditions.created_at[Op.lte] = endDate;
      }
    }

    // Arama terimi ile filtreleme
    if (searchTerm) {
      conditions[Op.or] = [
        { reservation_number: { [Op.like]: `%${searchTerm}%` } },
        { agency_reference: { [Op.like]: `%${searchTerm}%` } },
        sequelize.literal(`EXISTS (SELECT 1 FROM passengers WHERE
          passengers.reservation_id = Reservation.id AND
          (passengers.first_name LIKE '%${searchTerm}%' OR passengers.last_name LIKE '%${searchTerm}%'))`)
      ];
    }

    // Acente kullanıcıları sadece kendi acentelerinin rezervasyonlarını görebilir
    if (user.role === 'agent') {
      // Acente ID'sine sahipse
      if (user.agency_id) {
        conditions.agency_id = user.agency_id;
      } else {
        throw new AppError('Bu işlem için yetkiniz yok', 403);
      }
    }

    // Sayfalama ve sıralama
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;
    const offset = (pageNum - 1) * limitNum;
    const order = [[sortBy, sortDir]];

    const { count, rows: reservations } = await Reservation.findAndCountAll({
      where: conditions,
      include: [
        { model: Agency, as: 'agency' },
        { model: Region, as: 'fromRegion' },
        { model: Region, as: 'toRegion' },
        { model: VehicleType, as: 'vehicleType' },
        { model: User, as: 'creator', attributes: ['id', 'username', 'full_name'] }
      ],
      order,
      limit: limitNum,
      offset
    });

    // Sayfalama meta verisi
    const totalPages = Math.ceil(count / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrevious = pageNum > 1;

    return {
      reservations,
      pagination: {
        total: count,
        totalPages,
        currentPage: pageNum,
        limit: limitNum,
        hasNext,
        hasPrevious
      }
    };
  };

  /**
   * Rezervasyon detayı getirme servisi
   * @param {number} id - Rezervasyon ID
   * @param {Object} user - Kullanıcı bilgileri
   * @returns {Object} Rezervasyon detayları
   */
  exports.getReservationById = async (id, user) => {
    const reservation = await Reservation.findByPk(id, {
      include: [
        { model: Agency, as: 'agency' },
        { model: Region, as: 'fromRegion' },
        { model: Region, as: 'toRegion' },
        // Hotel modeli kaldırıldı
        { model: VehicleType, as: 'vehicleType' },
        { model: User, as: 'creator', attributes: ['id', 'username', 'full_name'] },
        {
          model: Transfer,
          as: 'transfers',
          include: [
            { model: Vehicle, as: 'vehicle' }
          ]
        },
        { model: Passenger, as: 'passengers' },
        { model: ContactDetail, as: 'contactDetails' },
        {
          model: ReservationLog,
          as: 'logs',
          include: [
            { model: User, as: 'user', attributes: ['id', 'username', 'full_name'] }
          ],
          order: [['action_time', 'DESC']]
        }
      ]
    });

    if (!reservation) {
      throw new AppError('Rezervasyon bulunamadı', 404);
    }

    // Acente kullanıcıları sadece kendi acentelerine ait rezervasyonları görebilir
    if (user.role === 'agent' && user.agency_id !== reservation.agency_id) {
      throw new AppError('Bu rezervasyona erişim izniniz yok', 403);
    }

    return reservation;
  };

  /**
   * Yeni rezervasyon oluşturma servisi
   * @param {Object} reservationData - Rezervasyon verileri
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Oluşturulan rezervasyon
   */
  exports.createReservation = async (reservationData, userId, ipAddress) => {
    const {
      agencyId,
      agencyReference,
      fromRegionId,
      toRegionId,
      hotelId,
      hotelName,
      secondaryHotelName,
      isRoundTrip,
      vehicleTypeId,
      price,
      currency,
      passengerCount,
      passengers,
      contactDetails,
      transfers
    } = reservationData;

    const transaction = await sequelize.transaction();

    try {
      // Temel doğrulamalar
      if (!agencyId || !fromRegionId || !toRegionId || !vehicleTypeId || !price || !passengerCount) {
        throw new AppError('Tüm zorunlu alanları doldurun', 400);
      }

      // Otel bilgisi kontrolü
      if (!hotelId && !hotelName) {
        throw new AppError('Otel bilgisi gereklidir', 400);
      }

      // Yolcu sayısı ve araç uyumluluğu kontrolü
      const vehicleType = await VehicleType.findByPk(vehicleTypeId);
      if (vehicleType && passengerCount > vehicleType.capacity) {
        throw new AppError(`Seçilen araç tipi en fazla ${vehicleType.capacity} yolcu taşıyabilir`, 400);
      }

      // Acente kullanıcıları sadece kendi acenteleri için rezervasyon yapabilir
      const user = await User.findByPk(userId);
      if (user.role === 'agent' && user.agency_id !== agencyId) {
        throw new AppError('Sadece kendi acenteniz için rezervasyon yapabilirsiniz', 403);
      }

      // Benzersiz rezervasyon numarası oluştur
      const reservationNumber = await helpers.generateReservationNumber();

      // Otel adlarını doğru şekilde kullan
      const hotelNameToUse = hotelName || '';
      const secondaryHotelNameToUse = secondaryHotelName || '';

      // Rezervasyon oluştur
      const reservation = await Reservation.create({
        reservation_number: reservationNumber,
        agency_id: agencyId,
        agency_reference: agencyReference,
        from_region_id: fromRegionId,
        to_region_id: toRegionId,
        hotel_name: hotelNameToUse,
        secondary_hotel_name: secondaryHotelNameToUse,
        is_round_trip: isRoundTrip,
        vehicle_type_id: vehicleTypeId,
        price,
        currency,
        status: 'pending',
        passenger_count: passengerCount,
        created_by: userId
      }, { transaction });

      // Yolcu bilgilerini ekle
      if (passengers && passengers.length > 0) {
        for (const passenger of passengers) {
          await Passenger.create({
            reservation_id: reservation.id,
            first_name: passenger.firstName,
            last_name: passenger.lastName,
            nationality: passenger.nationality || 'USA',
            gender: passenger.gender || 'male'
          }, { transaction });
        }
      }

      // İletişim bilgilerini ekle
      if (contactDetails && Array.isArray(contactDetails) && contactDetails.length > 0) {
        // Birincil telefon ve e-posta bilgilerini bul
        let primaryEmail = '';
        let primaryPhone = '';

        // Birincil iletişim bilgilerini bul
        for (const contact of contactDetails) {
          if (contact.type === 'email' && contact.isPrimary) {
            primaryEmail = contact.value;
          } else if (contact.type === 'phone' && contact.isPrimary) {
            primaryPhone = contact.value;
          }
        }

        // Birincil iletişim bilgisi yoksa, ilk bulunanı kullan
        if (!primaryEmail) {
          const emailContact = contactDetails.find(c => c.type === 'email');
          if (emailContact) primaryEmail = emailContact.value;
        }

        if (!primaryPhone) {
          const phoneContact = contactDetails.find(c => c.type === 'phone');
          if (phoneContact) primaryPhone = phoneContact.value;
        }

        // İletişim bilgilerini kaydet
        await ContactDetail.create({
          reservation_id: reservation.id,
          phone: primaryPhone,
          email: primaryEmail
        }, { transaction });
      }

      // Transfer detaylarını ekle
      if (transfers && transfers.length > 0) {
        for (const transfer of transfers) {
          await Transfer.create({
            reservation_id: reservation.id,
            is_return: transfer.isReturn,
            transfer_date: transfer.transferDate,
            transfer_time: transfer.transferTime,
            flight_number: transfer.flightNumber,
            pickup_location: transfer.pickupLocation,
            dropoff_location: transfer.dropoffLocation,
            room_number: transfer.roomNumber || '',
            baby_seat_count: transfer.babySeatCount || 0,
            child_seat_count: transfer.childSeatCount || 0,
            booster_count: transfer.boosterCount || 0,
            cash_payment: transfer.cashPayment || 0,
            cash_currency: transfer.cashCurrency || currency,
            driver_note: transfer.notes || '',
            operation_note: transfer.operationNote || '',
            status: 'pending'
          }, { transaction });
        }
      }

      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: reservation.id,
        user_id: userId,
        action_type: 'create',
        action_details: 'Rezervasyon oluşturuldu',
        ip_address: ipAddress
      }, { transaction });

      await transaction.commit();

      // Oluşturulan rezervasyonu getir
      return getReservationById(reservation.id, user);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  /**
   * Rezervasyon güncelleme servisi
   * @param {number} id - Rezervasyon ID
   * @param {Object} updateData - Güncelleme verileri
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Güncellenmiş rezervasyon
   */
  exports.updateReservation = async (id, updateData, userId, ipAddress) => {
    const transaction = await sequelize.transaction();

    try {
      // Rezervasyonu kontrol et
      const reservation = await Reservation.findByPk(id);

      if (!reservation) {
        throw new AppError('Rezervasyon bulunamadı', 404);
      }

      // Acente kullanıcıları sadece kendi acentelerinin rezervasyonlarını güncelleyebilir
      const user = await User.findByPk(userId);
      if (user.role === 'agent' && user.agency_id !== reservation.agency_id) {
        throw new AppError('Bu rezervasyonu güncelleme yetkiniz yok', 403);
      }

      // Yolcu sayısı ve araç uyumluluğu kontrolü
      if (updateData.vehicleTypeId && updateData.passengerCount) {
        const vehicleType = await VehicleType.findByPk(updateData.vehicleTypeId);
        if (vehicleType && updateData.passengerCount > vehicleType.capacity) {
          throw new AppError(`Seçilen araç tipi en fazla ${vehicleType.capacity} yolcu taşıyabilir`, 400);
        }
      }

      // Güncellenecek alanları hazırla
      const fieldsToUpdate = {};
      if (updateData.agencyReference !== undefined) fieldsToUpdate.agency_reference = updateData.agencyReference;
      if (updateData.fromRegionId !== undefined) fieldsToUpdate.from_region_id = updateData.fromRegionId;
      if (updateData.toRegionId !== undefined) fieldsToUpdate.to_region_id = updateData.toRegionId;
      if (updateData.hotelName !== undefined) fieldsToUpdate.hotel_name = updateData.hotelName;
      if (updateData.secondaryHotelName !== undefined) fieldsToUpdate.secondary_hotel_name = updateData.secondaryHotelName;
      if (updateData.isRoundTrip !== undefined) fieldsToUpdate.is_round_trip = updateData.isRoundTrip;
      if (updateData.vehicleTypeId !== undefined) fieldsToUpdate.vehicle_type_id = updateData.vehicleTypeId;
      if (updateData.price !== undefined) fieldsToUpdate.price = updateData.price;
      if (updateData.currency !== undefined) fieldsToUpdate.currency = updateData.currency;
      if (updateData.passengerCount !== undefined) fieldsToUpdate.passenger_count = updateData.passengerCount;
      if (updateData.status !== undefined) fieldsToUpdate.status = updateData.status;

      // Rezervasyonu güncelle
      await reservation.update(fieldsToUpdate, { transaction });

      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: reservation.id,
        user_id: userId,
        action_type: 'update',
        action_details: 'Rezervasyon güncellendi',
        ip_address: ipAddress
      }, { transaction });

      await transaction.commit();

      // Güncellenmiş rezervasyonu getir
      return getReservationById(id, user);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  /**
   * Rezervasyon durumunu güncelleme servisi
   * @param {number} id - Rezervasyon ID
   * @param {string} status - Yeni durum
   * @param {string} notes - Not
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} Güncellenmiş rezervasyon
   */
  exports.updateReservationStatus = async (id, status, notes, userId, ipAddress) => {
    const transaction = await sequelize.transaction();

    try {
      // Durum kontrolü
      const validStatuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show', 'failed'];
      if (!validStatuses.includes(status)) {
        throw new AppError('Geçersiz durum değeri', 400);
      }

      // Rezervasyonu kontrol et
      const reservation = await Reservation.findByPk(id);

      if (!reservation) {
        throw new AppError('Rezervasyon bulunamadı', 404);
      }

      // Acente kullanıcıları sadece kendi acentelerinin rezervasyonlarını güncelleyebilir
      const user = await User.findByPk(userId);
      if (user.role === 'agent' && user.agency_id !== reservation.agency_id) {
        throw new AppError('Bu rezervasyonu güncelleme yetkiniz yok', 403);
      }

      // Rezervasyon durumunu güncelle
      await reservation.update({ status }, { transaction });

      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: reservation.id,
        user_id: userId,
        action_type: 'status_update',
        action_details: `Durum değiştirildi: ${reservation.status} -> ${status}` + (notes ? ` (Not: ${notes})` : ''),
        ip_address: ipAddress
      }, { transaction });

      await transaction.commit();

      return {
        id: reservation.id,
        reservation_number: reservation.reservation_number,
        status: status
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  /**
   * Rezervasyon iptal etme servisi
   * @param {number} id - Rezervasyon ID
   * @param {string} reason - İptal sebebi
   * @param {number} userId - Kullanıcı ID
   * @param {string} ipAddress - IP adresi
   * @returns {Object} İptal edilen rezervasyon
   */
  exports.cancelReservation = async (id, reason, userId, ipAddress) => {
    const transaction = await sequelize.transaction();

    try {
      // Rezervasyonu kontrol et
      const reservation = await Reservation.findByPk(id);

      if (!reservation) {
        throw new AppError('Rezervasyon bulunamadı', 404);
      }

      // Acente kullanıcıları sadece kendi acentelerinin rezervasyonlarını iptal edebilir
      const user = await User.findByPk(userId);
      if (user.role === 'agent' && user.agency_id !== reservation.agency_id) {
        throw new AppError('Bu rezervasyonu iptal etme yetkiniz yok', 403);
      }

      // Eğer rezervasyon zaten iptal edilmişse
      if (reservation.status === 'cancelled') {
        throw new AppError('Bu rezervasyon zaten iptal edilmiş', 400);
      }

      // Rezervasyon durumunu 'cancelled' olarak güncelle
      await reservation.update({ status: 'cancelled' }, { transaction });

      // İlgili transferleri de iptal et
      await Transfer.update(
        { status: 'cancelled' },
        {
          where: { reservation_id: id },
          transaction
        }
      );

      // Log kaydı oluştur
      await ReservationLog.create({
        reservation_id: reservation.id,
        user_id: userId,
        action_type: 'cancel',
        action_details: `Rezervasyon iptal edildi. Sebep: ${reason || 'Belirtilmedi'}`,
        ip_address: ipAddress
      }, { transaction });

      await transaction.commit();

      return {
        id: reservation.id,
        reservation_number: reservation.reservation_number,
        status: 'cancelled'
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  /**
   * Rezervasyon voucher oluşturma servisi
   * @param {number} id - Rezervasyon ID
   * @param {Object} user - Kullanıcı bilgileri
   * @returns {Object} Voucher verileri
   */
  exports.generateVoucher = async (id, user) => {
    const reservation = await getReservationById(id, user);

    // Burada PDF oluşturma işlemleri yapılabilir
    // Şimdilik sadece verileri döndürelim

    return {
      reservationNumber: reservation.reservation_number,
      agencyName: reservation.agency.name,
      customerName: reservation.passengers.length > 0
        ? `${reservation.passengers[0].first_name} ${reservation.passengers[0].last_name}`
        : 'Misafir',
      passengerCount: reservation.passenger_count,
      transfers: reservation.transfers.map(transfer => ({
        date: transfer.transfer_date,
        time: transfer.transfer_time,
        from: transfer.is_return ? reservation.hotel.name : reservation.fromRegion.name,
        to: transfer.is_return ? reservation.toRegion.name : reservation.hotel.name,
        vehicleType: reservation.vehicleType.name,
        flightNumber: transfer.flight_number,
        status: transfer.status
      })),
      contactDetails: reservation.contactDetails,
      generatedAt: new Date().toISOString()
    };
  };

  // Yardımcı fonksiyon
  async function getReservationById(id, user) {
    const reservation = await Reservation.findByPk(id, {
      include: [
        { model: Agency, as: 'agency' },
        { model: Region, as: 'fromRegion' },
        { model: Region, as: 'toRegion' },
        // Hotel modeli kaldırıldı
        { model: VehicleType, as: 'vehicleType' },
        { model: User, as: 'creator', attributes: ['id', 'username', 'full_name'] },
        {
          model: Transfer,
          as: 'transfers',
          include: [
            { model: Vehicle, as: 'vehicle' }
          ]
        },
        { model: Passenger, as: 'passengers' },
        { model: ContactDetail, as: 'contactDetails' }
      ]
    });

    if (!reservation) {
      throw new AppError('Rezervasyon bulunamadı', 404);
    }

    return reservation;
  }