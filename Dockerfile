FROM node:18-alpine

# Çalışma dizinini ayarla
WORKDIR /app

# Gerekli paketleri yükle
RUN apk add --no-cache wget curl

# Bağımlılıkları kopyala ve yükle
COPY package*.json ./
RUN npm install

# Kaynak kodları kopyala
COPY . .

# .env.production dosyasını .env olarak kopyala
RUN if [ -f .env.production ]; then cp .env.production .env; fi

# Dosya izinlerini kontrol et
RUN ls -la

# Uploads klasörü oluştur
RUN mkdir -p uploads

# Ortam değişkenlerini ayarla
ENV NODE_ENV=production
ENV PORT=3008

# Uygulamayı çalıştır
EXPOSE 3008

# Healthcheck ekle
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3008/health || exit 1

# Başlatma komutu
CMD ["npm", "start"]
