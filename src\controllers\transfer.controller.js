// server/controllers/transfer.controller.js

const transferService = require('../services/transfer.service');

/**
 * Tüm transferleri getir
 * @route GET /api/transfers
 */
exports.getAllTransfers = async (req, res, next) => {
  try {
    const {
      status,
      date,
      vehicleId,
      page = 1,
      limit = 20,
      sortBy = 'transfer_date',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      status,
      date,
      vehicleId,
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sortBy,
      sortDir
    };

    const result = await transferService.getAllTransfers(filters, req.user);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer detayını getir
 * @route GET /api/transfers/:id
 */
exports.getTransferById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const transfer = await transferService.getTransferById(id, req.user);

    res.status(200).json({
      success: true,
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni transfer oluştur
 * @route POST /api/transfers
 */
exports.createTransfer = async (req, res, next) => {
  try {
    const transferData = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const transfer = await transferService.createTransfer(
      transferData,
      userId,
      ipAddress
    );

    res.status(201).json({
      success: true,
      message: 'Transfer başarıyla oluşturuldu',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer güncelle
 * @route PUT /api/transfers/:id
 */
exports.updateTransfer = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const transfer = await transferService.updateTransfer(
      id,
      updateData,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Transfer başarıyla güncellendi',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer sil
 * @route DELETE /api/transfers/:id
 */
exports.deleteTransfer = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const ipAddress = req.ip;

    await transferService.deleteTransfer(id, userId, ipAddress);

    res.status(200).json({
      success: true,
      message: 'Transfer başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfere taşıyıcı firma ata
 * @route PATCH /api/transfers/:id/assign-carrier
 */
exports.assignCarrier = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { carrierId } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const transfer = await transferService.assignCarrier(
      id,
      carrierId,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Taşıyıcı firma başarıyla atandı',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfere araç ata
 * @route PATCH /api/transfers/:id/assign-vehicle
 */
exports.assignVehicle = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { vehicleId } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const transfer = await transferService.assignVehicle(
      id,
      vehicleId,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Araç başarıyla atandı',
      transfer
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transfer durumunu güncelle
 * @route PATCH /api/transfers/:id/status
 */
exports.updateStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;

    const transfer = await transferService.updateStatus(
      id,
      status,
      userId,
      ipAddress
    );

    res.status(200).json({
      success: true,
      message: 'Durum başarıyla güncellendi',
      transfer
    });
  } catch (error) {
    next(error);
  }
};
