module.exports = (sequelize, DataTypes) => {
  const Reservation = sequelize.define('Reservation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    reservation_number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true
    },
    agency_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'agencies',
        key: 'id'
      }
    },
    agency_reference: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    from_region_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'regions',
        key: 'id'
      }
    },
    to_region_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'regions',
        key: 'id'
      }
    },
    hotel_name: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    secondary_hotel_name: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    is_round_trip: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    vehicle_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicle_types',
        key: 'id'
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'EUR'
    },
    status: {
      type: DataTypes.ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show', 'failed'),
      defaultValue: 'pending'
    },
    passenger_count: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'reservations',
    timestamps: false,
    hooks: {
      beforeUpdate: (reservation) => {
        reservation.updated_at = new Date();
      }
    }
  });

  return Reservation;
};