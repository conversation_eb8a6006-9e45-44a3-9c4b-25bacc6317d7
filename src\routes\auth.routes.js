// server/routes/auth.routes.js

const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../middleware/validate');
const { authSchema } = require('../validators/auth.validator');

/**
 * @route POST /api/auth/login
 * @desc Kullanıcı girişi
 * @access Public
 */
router.post(
  '/login',
  validate(authSchema.login),
  authController.login
);

// OPTIONS istekleri için preflight yanıtı
router.options('/login', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.sendStatus(200);
});

/**
 * @route GET /api/auth/me
 * @desc Mevcut kullanıcı bilgilerini getir
 * @access Private
 */
router.get(
  '/me',
  authenticate,
  authController.getCurrentUser
);

/**
 * @route POST /api/auth/change-password
 * @desc Şifre değiştirme
 * @access Private
 */
router.post(
  '/change-password',
  authenticate,
  validate(authSchema.changePassword),
  authController.changePassword
);

/**
 * @route POST /api/auth/register
 * @desc Yeni kullanıcı kaydı (sadece admin)
 * @access Private
 */
router.post(
  '/register',
  authenticate,
  authorize(['admin']),
  validate(authSchema.register),
  authController.registerUser
);

/**
 * @route POST /api/auth/forgot-password
 * @desc Şifremi unuttum
 * @access Public
 */
router.post(
  '/forgot-password',
  validate(authSchema.forgotPassword),
  authController.forgotPassword
);

/**
 * @route POST /api/auth/reset-password
 * @desc Şifre sıfırlama
 * @access Public
 */
router.post(
  '/reset-password',
  validate(authSchema.resetPassword),
  authController.resetPassword
);

/**
 * @route POST /api/auth/logout
 * @desc Kullanıcı çıkışı
 * @access Private
 */
router.post(
  '/logout',
  authenticate,
  authController.logout
);

module.exports = router;
