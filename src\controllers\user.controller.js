// server/controllers/user.controller.js

const userService = require('../services/user.service');

/**
 * <PERSON><PERSON><PERSON> kull<PERSON>ı getir
 * @route GET /api/users
 */
exports.getAllUsers = async (req, res, next) => {
  try {
    const { 
      username, 
      email, 
      role,
      isActive,
      page = 1, 
      limit = 20,
      sortBy = 'username',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      username,
      email,
      role,
      isActive,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await userService.getAllUsers(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı detaylarını getir
 * @route GET /api/users/:id
 */
exports.getUserById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const user = await userService.getUserById(id);
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * <PERSON><PERSON> kullanıcı oluştur
 * @route POST /api/users
 */
exports.createUser = async (req, res, next) => {
  try {
    const { 
      username, 
      password, 
      email, 
      fullName, 
      phone, 
      role,
      isActive,
      permissions 
    } = req.body;
    
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      phone,
      role,
      isActive,
      permissions,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: newUser,
      message: 'Kullanıcı başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı güncelle
 * @route PUT /api/users/:id
 */
exports.updateUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      username, 
      email, 
      fullName, 
      phone, 
      role
    } = req.body;
    
    const updatedUser = await userService.updateUser(id, {
      username,
      email,
      fullName,
      phone,
      role
    });
    
    res.status(200).json({
      success: true,
      data: updatedUser,
      message: 'Kullanıcı başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı sil
 * @route DELETE /api/users/:id
 */
exports.deleteUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Kendini silmeye çalışıyorsa engelle
    if (id == req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Kendi hesabınızı silemezsiniz'
      });
    }
    
    await userService.deleteUser(id);
    
    res.status(200).json({
      success: true,
      message: 'Kullanıcı başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı durumunu güncelle (aktif/pasif)
 * @route PATCH /api/users/:id/status
 */
exports.updateUserStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;
    
    // Kendini devre dışı bırakmaya çalışıyorsa engelle
    if (id == req.user.id && isActive === false) {
      return res.status(400).json({
        success: false,
        message: 'Kendi hesabınızı devre dışı bırakamazsınız'
      });
    }
    
    const updatedUser = await userService.updateUserStatus(id, isActive);
    
    res.status(200).json({
      success: true,
      data: updatedUser,
      message: `Kullanıcı durumu ${isActive ? 'aktif' : 'pasif'} olarak güncellendi`
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Kullanıcı izinlerini güncelle
 * @route PUT /api/users/:id/permissions
 */
exports.updateUserPermissions = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { permissions } = req.body;
    
    const updatedPermissions = await userService.updateUserPermissions(id, permissions);
    
    res.status(200).json({
      success: true,
      data: updatedPermissions,
      message: 'Kullanıcı izinleri başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Role göre kullanıcıları getir
 * @route GET /api/users/role/:role
 */
exports.getUsersByRole = async (req, res, next) => {
  try {
    const { role } = req.params;
    const { isActive } = req.query;
    
    const users = await userService.getUsersByRole(role, isActive);
    
    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    next(error);
  }
};
