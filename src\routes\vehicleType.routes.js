// server/routes/vehicleType.routes.js

const express = require('express');
const router = express.Router();
const vehicleTypeController = require('../controllers/vehicleType.controller');
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route GET /api/vehicle-types
 * @desc Tüm araç tiplerini getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  vehicleTypeController.getAllVehicleTypes
);

/**
 * @route GET /api/vehicle-types/:id
 * @desc Araç tipi detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  vehicleTypeController.getVehicleTypeById
);

/**
 * @route POST /api/vehicle-types
 * @desc Yeni araç tipi oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'manager']), 
  vehicleTypeController.createVehicleType
);

/**
 * @route PUT /api/vehicle-types/:id
 * @desc Araç tipi güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']), 
  vehicleTypeController.updateVehicleType
);

/**
 * @route DELETE /api/vehicle-types/:id
 * @desc Araç tipi sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']), 
  vehicleTypeController.deleteVehicleType
);

module.exports = router;
