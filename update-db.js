const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const config = require('./src/config/config');

async function updateDatabase() {
  try {
    // SQL dosyasını oku
    const sqlFile = path.join(__dirname, 'update-schema.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    // SQL komutlarını ayır
    const commands = sql
      .replace(/--.*$/gm, '') // Yorumları kaldır
      .split(';')
      .filter(cmd => cmd.trim() !== '');
    
    // Veritabanına bağlan
    const connection = await mysql.createConnection({
      database: process.env.DB_NAME || 'operasyon_sistemi',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
    });
    
    console.log('Veritabanına bağlandı');
    
    // Her komutu çalıştır
    for (const command of commands) {
      console.log(`Çalıştırılıyor: ${command}`);
      await connection.query(command);
    }
    
    console.log('Veritabanı şeması başarıyla güncellendi!');
    await connection.end();
    process.exit(0);
  } catch (error) {
    console.error('Veritabanı güncellenirken hata oluştu:', error);
    process.exit(1);
  }
}

updateDatabase();
