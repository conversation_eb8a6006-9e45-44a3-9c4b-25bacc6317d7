// server/utils/helpers.js

/**
 * Rezervasyon numarası oluşturma
 * @param {string} prefix - Önek (örn: 'RES')
 * @returns {string} Benzersiz rezervasyon numarası
 */
exports.generateReservationNumber = (prefix = 'RES') => {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `${prefix}${year}${month}${day}${random}`;
};

/**
 * Tarih formatını değiştirme
 * @param {Date|string} date - Tarih
 * @param {string} format - Format (default: 'YYYY-MM-DD')
 * @returns {string} Formatlanmış tarih
 */
exports.formatDate = (date, format = 'YYYY-MM-DD') => {
  const d = new Date(date);
  
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  const seconds = d.getSeconds().toString().padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * Para birimi formatı
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @returns {string} Formatlanmış para
 */
exports.formatCurrency = (amount, currency = 'EUR') => {
  const formatter = new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2
  });
  
  return formatter.format(amount);
};

/**
 * Sayfalama yardımcısı
 * @param {number} total - Toplam öğe sayısı
 * @param {number} page - Mevcut sayfa
 * @param {number} limit - Sayfa başına öğe sayısı
 * @returns {Object} Sayfalama bilgileri
 */
exports.getPagination = (total, page, limit) => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;
  
  return {
    total,
    totalPages,
    currentPage: page,
    limit,
    hasNextPage,
    hasPrevPage
  };
};

/**
 * Dosya uzantısını kontrol etme
 * @param {string} filename - Dosya adı
 * @param {Array} allowedExtensions - İzin verilen uzantılar
 * @returns {boolean} Uzantı geçerli mi
 */
exports.isValidFileExtension = (filename, allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx']) => {
  const ext = filename.substring(filename.lastIndexOf('.')).toLowerCase();
  return allowedExtensions.includes(ext);
};

/**
 * Güvenli dosya adı oluşturma
 * @param {string} filename - Orijinal dosya adı
 * @returns {string} Güvenli dosya adı
 */
exports.getSafeFilename = (filename) => {
  // Dosya adındaki özel karakterleri ve boşlukları temizle
  const name = filename.replace(/[^a-z0-9.]/gi, '_').toLowerCase();
  
  // Benzersiz bir önek ekle
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  
  // Uzantıyı al
  const ext = name.substring(name.lastIndexOf('.'));
  const baseName = name.substring(0, name.lastIndexOf('.'));
  
  return `${baseName}_${timestamp}_${random}${ext}`;
};
