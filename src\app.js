require("dotenv").config({});

const express = require('express');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const AppError = require('./utils/appError');

// Express app oluştur
const app = express();

// Global middleware'ler
// Güvenlik başlıkları - CORS kısıtlamalarını kaldır
app.use(helmet({
  crossOriginResourcePolicy: false,
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  contentSecurityPolicy: false
}));
app.use(compression()); // Yanıt sıkıştırma
// CORS yapılandırması
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

  // OPTIONS istekleri için preflight yanıtı
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
});
app.use(express.json({ limit: '10mb' })); // JSON body parser
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL-encoded body parser

// Statik dosyalar
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Loglama
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Router imports
const authRoutes = require('./routes/auth.routes');
const reservationRoutes = require('./routes/reservation.routes');
const operationRoutes = require('./routes/operation.routes');
const agencyRoutes = require('./routes/agency.routes');
const carrierRoutes = require('./routes/carrier.routes');
const regionRoutes = require('./routes/region.routes');
const vehicleRoutes = require('./routes/vehicle.routes');
const userRoutes = require('./routes/user.routes');
const reportRoutes = require('./routes/report.routes');
const vehicleTypeRoutes = require('./routes/vehicleType.routes');
const priceRoutes = require('./routes/price.routes');
const accountRoutes = require('./routes/account.routes');
const transactionRoutes = require('./routes/transaction.routes');
const documentRoutes = require('./routes/document.routes');
const exchangeRateRoutes = require('./routes/exchangeRate.routes');
const transferRoutes = require('./routes/transfer.routes');

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/reservations', reservationRoutes);
app.use('/api/operations', operationRoutes);
app.use('/api/agencies', agencyRoutes);
app.use('/api/carriers', carrierRoutes);
app.use('/api/regions', regionRoutes);
app.use('/api/vehicles', vehicleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/vehicle-types', vehicleTypeRoutes);
app.use('/api/prices', priceRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/documents', documentRoutes);
app.use('/api/exchange-rates', exchangeRateRoutes);
app.use('/api/transfers', transferRoutes);

// Ana sayfa
app.get("/", (req, res) => {
  res.send("⚡️ CVR TOURISM & TRASNFER API");
});

// Sağlık kontrolü
app.get("/health", (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime()
  });
});

// 404 hatası
app.all('*', (req, res, next) => {
  next(new AppError(`${req.originalUrl} bulunamadı`, 404));
});

// Global hata işleyici
app.use((err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // Geliştirme ortamında detaylı hata
  if (process.env.NODE_ENV === 'development') {
    res.status(err.statusCode).json({
      success: false,
      status: err.status,
      message: err.message,
      stack: err.stack,
      error: err
    });
  }
  // Üretim ortamında sadece gerekli bilgiler
  else {
    // Operasyonel hatalar: güvenilir hatalar
    if (err.isOperational) {
      res.status(err.statusCode).json({
        success: false,
        status: err.status,
        message: err.message
      });
    }
    // Programlama veya bilinmeyen hatalar
    else {
      console.error('ERROR 💥', err);
      res.status(500).json({
        success: false,
        status: 'error',
        message: 'Bir şeyler yanlış gitti!'
      });
    }
  }
});

module.exports = app;