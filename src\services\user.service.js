// server/services/user.service.js

const { 
  User, 
  Permission, 
  sequelize 
} = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const AppError = require('../utils/appError');
const emailService = require('./email.service');

/**
 * Tüm kullanıcıları listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Kullanıcı listesi ve sayfalama bilgileri
 */
exports.getAllUsers = async (filters) => {
  // Filtreleme koşulları
  const whereConditions = {};

  if (filters.username) whereConditions.username = { [Op.like]: `%${filters.username}%` };
  if (filters.email) whereConditions.email = { [Op.like]: `%${filters.email}%` };
  if (filters.role) whereConditions.role = filters.role;
  if (filters.isActive !== undefined) {
    whereConditions.is_active = filters.isActive === 'true' || filters.isActive === true;
  }

  // Sayfalama
  const page = parseInt(filters.page) || 1;
  const limit = parseInt(filters.limit) || 10;
  const offset = (page - 1) * limit;
  
  // Sıralama
  const sortBy = filters.sortBy || 'username';
  const sortDir = filters.sortDir || 'ASC';
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await User.findAndCountAll({
    where: whereConditions,
    attributes: { exclude: ['password'] },
    include: [
      { model: Permission, as: 'permissions' }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    users: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Kullanıcı detaylarını getirme servisi
 * @param {number} id - Kullanıcı ID
 * @returns {Object} Kullanıcı detayları
 */
exports.getUserById = async (id) => {
  const user = await User.findByPk(id, {
    attributes: { exclude: ['password'] },
    include: [
      { model: Permission, as: 'permissions' }
    ]
  });
  
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }
  
  return user;
};

/**
 * Yeni kullanıcı oluşturma servisi
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Object} Oluşturulan kullanıcı
 */
exports.createUser = async (userData) => {
  const { 
    username, 
    password, 
    email, 
    fullName, 
    phone, 
    role,
    isActive = true,
    permissions = {},
    createdBy
  } = userData;

  // Kullanıcı adı ve e-posta kontrolü
  const existingUser = await User.findOne({
    where: {
      [Op.or]: [
        { username },
        { email }
      ]
    }
  });

  if (existingUser) {
    if (existingUser.username === username) {
      throw new AppError('Bu kullanıcı adı zaten kullanılıyor', 400);
    }
    if (existingUser.email === email) {
      throw new AppError('Bu e-posta adresi zaten kullanılıyor', 400);
    }
  }

  // Rol kontrolü
  const validRoles = ['admin', 'manager', 'operation', 'finance', 'agent', 'driver'];
  if (!validRoles.includes(role)) {
    throw new AppError('Geçersiz rol', 400);
  }

  // Şifreyi hash'le
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // İşlem başlat
  const transaction = await sequelize.transaction();
  
  try {
    // Yeni kullanıcı oluştur
    const newUser = await User.create({
      username,
      password: hashedPassword,
      email,
      full_name: fullName,
      phone,
      role,
      is_active: isActive
    }, { transaction });
    
    // İzinleri oluştur
    await Permission.create({
      user_id: newUser.id,
      can_access_data: permissions.canAccessData || false,
      can_manage_prices: permissions.canManagePrices || false,
      can_manage_reservations: permissions.canManageReservations || false,
      can_manage_operations: permissions.canManageOperations || false,
      can_manage_payments: permissions.canManagePayments || false
    }, { transaction });
    
    await transaction.commit();
    
    // Hoş geldiniz e-postası gönder
    try {
      await emailService.sendWelcomeEmail(email, {
        name: fullName,
        username,
        loginUrl: `${process.env.FRONTEND_URL}/login`
      });
    } catch (error) {
      console.error('E-posta gönderme hatası:', error);
      // E-posta gönderme hatası kullanıcı oluşturmayı engellemez
    }
    
    // Şifre hariç kullanıcı bilgilerini döndür
    const userWithoutPassword = await User.findByPk(newUser.id, {
      attributes: { exclude: ['password'] },
      include: [
        { model: Permission, as: 'permissions' }
      ]
    });
    
    return userWithoutPassword;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Kullanıcı güncelleme servisi
 * @param {number} id - Kullanıcı ID
 * @param {Object} userData - Güncellenecek veriler
 * @returns {Object} Güncellenen kullanıcı
 */
exports.updateUser = async (id, userData) => {
  const { 
    username, 
    email, 
    fullName, 
    phone, 
    role
  } = userData;

  // Kullanıcıyı bul
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Kullanıcı adı ve e-posta kontrolü (eğer değiştiyse)
  if (username && username !== user.username) {
    const existingUsername = await User.findOne({
      where: { 
        username,
        id: { [Op.ne]: id }
      }
    });

    if (existingUsername) {
      throw new AppError('Bu kullanıcı adı zaten kullanılıyor', 400);
    }
  }
  
  if (email && email !== user.email) {
    const existingEmail = await User.findOne({
      where: { 
        email,
        id: { [Op.ne]: id }
      }
    });

    if (existingEmail) {
      throw new AppError('Bu e-posta adresi zaten kullanılıyor', 400);
    }
  }

  // Rol kontrolü (eğer değiştiyse)
  if (role && role !== user.role) {
    const validRoles = ['admin', 'manager', 'operation', 'finance', 'agent', 'driver'];
    if (!validRoles.includes(role)) {
      throw new AppError('Geçersiz rol', 400);
    }
  }

  // Güncelle
  await user.update({
    username: username || user.username,
    email: email || user.email,
    full_name: fullName || user.full_name,
    phone: phone !== undefined ? phone : user.phone,
    role: role || user.role
  });

  // Şifre hariç kullanıcı bilgilerini döndür
  const userWithoutPassword = await User.findByPk(id, {
    attributes: { exclude: ['password'] },
    include: [
      { model: Permission, as: 'permissions' }
    ]
  });
  
  return userWithoutPassword;
};

/**
 * Kullanıcı silme servisi
 * @param {number} id - Kullanıcı ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteUser = async (id) => {
  // Kullanıcıyı bul
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Admin kullanıcısını silmeye çalışıyorsa engelle
  if (user.role === 'admin' && user.username === 'admin') {
    throw new AppError('Ana admin kullanıcısı silinemez', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();
  
  try {
    // İzinleri sil
    await Permission.destroy({
      where: { user_id: id },
      transaction
    });
    
    // Kullanıcıyı sil
    await user.destroy({ transaction });
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Kullanıcı durumunu güncelleme servisi
 * @param {number} id - Kullanıcı ID
 * @param {boolean} isActive - Aktif durumu
 * @returns {Object} Güncellenen kullanıcı
 */
exports.updateUserStatus = async (id, isActive) => {
  // Kullanıcıyı bul
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Admin kullanıcısını devre dışı bırakmaya çalışıyorsa engelle
  if (user.role === 'admin' && user.username === 'admin' && !isActive) {
    throw new AppError('Ana admin kullanıcısı devre dışı bırakılamaz', 400);
  }

  // Güncelle
  await user.update({
    is_active: isActive
  });

  // Şifre hariç kullanıcı bilgilerini döndür
  const userWithoutPassword = await User.findByPk(id, {
    attributes: { exclude: ['password'] },
    include: [
      { model: Permission, as: 'permissions' }
    ]
  });
  
  return userWithoutPassword;
};

/**
 * Kullanıcı izinlerini güncelleme servisi
 * @param {number} id - Kullanıcı ID
 * @param {Object} permissionData - İzin verileri
 * @returns {Object} Güncellenen izinler
 */
exports.updateUserPermissions = async (id, permissionData) => {
  // Kullanıcıyı bul
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // İzinleri bul
  let permissions = await Permission.findOne({
    where: { user_id: id }
  });
  
  // İzinler yoksa oluştur
  if (!permissions) {
    permissions = await Permission.create({
      user_id: id,
      can_access_data: false,
      can_manage_prices: false,
      can_manage_reservations: false,
      can_manage_operations: false,
      can_manage_payments: false
    });
  }

  // İzinleri güncelle
  await permissions.update({
    can_access_data: permissionData.canAccessData !== undefined ? permissionData.canAccessData : permissions.can_access_data,
    can_manage_prices: permissionData.canManagePrices !== undefined ? permissionData.canManagePrices : permissions.can_manage_prices,
    can_manage_reservations: permissionData.canManageReservations !== undefined ? permissionData.canManageReservations : permissions.can_manage_reservations,
    can_manage_operations: permissionData.canManageOperations !== undefined ? permissionData.canManageOperations : permissions.can_manage_operations,
    can_manage_payments: permissionData.canManagePayments !== undefined ? permissionData.canManagePayments : permissions.can_manage_payments
  });

  return permissions;
};

/**
 * Role göre kullanıcıları getirme servisi
 * @param {string} role - Kullanıcı rolü
 * @param {boolean} isActive - Aktif durumu (opsiyonel)
 * @returns {Array} Kullanıcı listesi
 */
exports.getUsersByRole = async (role, isActive) => {
  // Rol kontrolü
  const validRoles = ['admin', 'manager', 'operation', 'finance', 'agent', 'driver'];
  if (!validRoles.includes(role)) {
    throw new AppError('Geçersiz rol', 400);
  }

  // Filtreleme koşulları
  const whereConditions = { role };
  
  if (isActive !== undefined) {
    whereConditions.is_active = isActive;
  }

  // Kullanıcıları getir
  const users = await User.findAll({
    where: whereConditions,
    attributes: { exclude: ['password'] },
    order: [['full_name', 'ASC']]
  });

  return users;
};
