module.exports = (sequelize, DataTypes) => {
  const Vehicle = sequelize.define('Vehicle', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    plate_number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true
    },
    vehicle_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicle_types',
        key: 'id'
      }
    },
    carrier_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'carriers',
        key: 'id'
      }
    },
    driver_name: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    driver_phone: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    driver_photo: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    vehicle_photo: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    driver_rating: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0
    }
  }, {
    tableName: 'vehicles',
    timestamps: false
  });

  return Vehicle;
};