module.exports = (sequelize, DataTypes) => {
  const Price = sequelize.define('Price', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    agency_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'agencies',
        key: 'id'
      }
    },
    carrier_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'carriers',
        key: 'id'
      }
    },
    from_region_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'regions',
        key: 'id'
      }
    },
    to_region_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'regions',
        key: 'id'
      }
    },
    vehicle_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicle_types',
        key: 'id'
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'EUR'
    },
    valid_from: {
      type: DataTypes.DATE,
      allowNull: true
    },
    valid_to: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'prices',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['agency_id', 'carrier_id', 'from_region_id', 'to_region_id', 'vehicle_type_id', 'valid_from']
      }
    ]
  });

  return Price;
};
