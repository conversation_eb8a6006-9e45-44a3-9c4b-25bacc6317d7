// server/routes/vehicle.routes.js

const express = require('express');
const router = express.Router();
const vehicleController = require('../controllers/vehicle.controller');
const { authenticate, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');

/**
 * @route GET /api/vehicles
 * @desc Tüm araçları getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  vehicleController.getAllVehicles
);

/**
 * @route GET /api/vehicles/:id
 * @desc Araç detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  vehicleController.getVehicleById
);

/**
 * @route POST /api/vehicles
 * @desc Yeni araç oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  authorize(['admin', 'manager']), 
  vehicleController.createVehicle
);

/**
 * @route PUT /api/vehicles/:id
 * @desc Araç güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']), 
  vehicleController.updateVehicle
);

/**
 * @route DELETE /api/vehicles/:id
 * @desc Araç sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']), 
  vehicleController.deleteVehicle
);

/**
 * @route GET /api/vehicles/carrier/:carrierId
 * @desc Taşımacıya ait araçları getir
 * @access Private
 */
router.get(
  '/carrier/:carrierId', 
  authenticate, 
  vehicleController.getVehiclesByCarrier
);

/**
 * @route GET /api/vehicles/type/:typeId
 * @desc Tipe göre araçları getir
 * @access Private
 */
router.get(
  '/type/:typeId', 
  authenticate, 
  vehicleController.getVehiclesByType
);

/**
 * @route POST /api/vehicles/:id/photos
 * @desc Araç fotoğrafı yükle
 * @access Private
 */
router.post(
  '/:id/photos', 
  authenticate, 
  authorize(['admin', 'manager']), 
  upload.single('vehiclePhoto'),
  vehicleController.uploadVehiclePhoto
);

/**
 * @route POST /api/vehicles/:id/driver-photo
 * @desc Sürücü fotoğrafı yükle
 * @access Private
 */
router.post(
  '/:id/driver-photo', 
  authenticate, 
  authorize(['admin', 'manager']), 
  upload.single('driverPhoto'),
  vehicleController.uploadDriverPhoto
);

/**
 * @route GET /api/vehicles/available
 * @desc Belirli tarih ve saatte müsait araçları getir
 * @access Private
 */
router.get(
  '/available', 
  authenticate, 
  vehicleController.getAvailableVehicles
);

module.exports = router;
