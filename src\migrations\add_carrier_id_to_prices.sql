-- MySQL için carrier_id sütunu ekleme ve indeks güncelleme scripti

-- Önce mevcut unique indeksi kaldır
ALTER TABLE prices DROP INDEX prices_agency_id_from_region_id_to_region_id_vehicle_type_id_valid_from_unique;

-- carrier_id sütununu ekle
ALTER TABLE prices ADD COLUMN carrier_id INT;

-- carrier_id için foreign key constraint ekle
ALTER TABLE prices ADD CONSTRAINT fk_prices_carrier_id FOREIGN KEY (carrier_id) REFERENCES carriers(id);

-- <PERSON><PERSON> unique indeksi ekle
ALTER TABLE prices ADD UNIQUE INDEX prices_unique_index (agency_id, carrier_id, from_region_id, to_region_id, vehicle_type_id, valid_from);

-- Geri alma (rollback) için:
/*
-- Yeni indeksi kaldır
ALTER TABLE prices DROP INDEX prices_unique_index;

-- Eski indeksi geri ekle
ALTER TABLE prices ADD UNIQUE INDEX prices_agency_id_from_region_id_to_region_id_vehicle_type_id_valid_from_unique (agency_id, from_region_id, to_region_id, vehicle_type_id, valid_from);

-- Foreign key constraint'i kaldır
ALTER TABLE prices DROP FOREIGN KEY fk_prices_carrier_id;

-- carrier_id sütununu kaldır
ALTER TABLE prices DROP COLUMN carrier_id;
*/
