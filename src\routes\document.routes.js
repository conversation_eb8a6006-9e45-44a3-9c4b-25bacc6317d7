// server/routes/document.routes.js

const express = require('express');
const router = express.Router();
const documentController = require('../controllers/document.controller');
const { authenticate, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');

/**
 * @route GET /api/documents
 * @desc Tüm belgeleri getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  documentController.getAllDocuments
);

/**
 * @route GET /api/documents/:id
 * @desc Belge detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  documentController.getDocumentById
);

/**
 * @route POST /api/documents
 * @desc Yeni belge yükle
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  upload.single('document'),
  documentController.uploadDocument
);

/**
 * @route DELETE /api/documents/:id
 * @desc Belge sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager']),
  documentController.deleteDocument
);

/**
 * @route GET /api/documents/entity/:type/:id
 * @desc Belirli bir varlığa ait belgeleri getir
 * @access Private
 */
router.get(
  '/entity/:type/:id', 
  authenticate, 
  documentController.getDocumentsByEntity
);

/**
 * @route GET /api/documents/download/:id
 * @desc Belge indir
 * @access Private
 */
router.get(
  '/download/:id', 
  authenticate, 
  documentController.downloadDocument
);

module.exports = router;
