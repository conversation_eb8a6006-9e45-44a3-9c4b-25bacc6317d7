// server/controllers/exchangeRate.controller.js

const exchangeRateService = require('../services/exchangeRate.service');

/**
 * Tüm döviz kurlarını getir
 * @route GET /api/exchange-rates
 */
exports.getAllExchangeRates = async (req, res, next) => {
  try {
    const { 
      fromCurrency, 
      toCurrency, 
      startDate, 
      endDate,
      page = 1, 
      limit = 20,
      sortBy = 'effective_date',
      sortDir = 'DESC'
    } = req.query;

    const filters = {
      fromCurrency,
      toCurrency,
      startDate,
      endDate,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await exchangeRateService.getAllExchangeRates(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Güncel döviz kurlarını getir
 * @route GET /api/exchange-rates/current
 */
exports.getCurrentRates = async (req, res, next) => {
  try {
    const { baseCurrency = 'EUR' } = req.query;
    
    const rates = await exchangeRateService.getCurrentRates(baseCurrency);
    
    res.status(200).json({
      success: true,
      data: rates
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İki para birimi arasındaki kuru getir
 * @route GET /api/exchange-rates/:from/:to
 */
exports.getRate = async (req, res, next) => {
  try {
    const { from, to } = req.params;
    const { date } = req.query;
    
    const rate = await exchangeRateService.getRate(from, to, date);
    
    res.status(200).json({
      success: true,
      data: rate
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni döviz kuru oluştur
 * @route POST /api/exchange-rates
 */
exports.createExchangeRate = async (req, res, next) => {
  try {
    const { fromCurrency, toCurrency, rate, effectiveDate } = req.body;
    
    const newRate = await exchangeRateService.createExchangeRate({
      fromCurrency,
      toCurrency,
      rate,
      effectiveDate
    });
    
    res.status(201).json({
      success: true,
      data: newRate,
      message: 'Döviz kuru başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Döviz kuru güncelle
 * @route PUT /api/exchange-rates/:id
 */
exports.updateExchangeRate = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { rate } = req.body;
    
    const updatedRate = await exchangeRateService.updateExchangeRate(id, {
      rate
    });
    
    res.status(200).json({
      success: true,
      data: updatedRate,
      message: 'Döviz kuru başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Döviz kuru sil
 * @route DELETE /api/exchange-rates/:id
 */
exports.deleteExchangeRate = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await exchangeRateService.deleteExchangeRate(id);
    
    res.status(200).json({
      success: true,
      message: 'Döviz kuru başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Döviz kurlarını dış API'den senkronize et
 * @route POST /api/exchange-rates/sync
 */
exports.syncExchangeRates = async (req, res, next) => {
  try {
    const { baseCurrency = 'EUR' } = req.body;
    
    const result = await exchangeRateService.syncExchangeRates(baseCurrency);
    
    res.status(200).json({
      success: true,
      data: result,
      message: 'Döviz kurları başarıyla senkronize edildi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Para birimi dönüşümü yap
 * @route POST /api/exchange-rates/convert
 */
exports.convertCurrency = async (req, res, next) => {
  try {
    const { amount, fromCurrency, toCurrency, date } = req.body;
    
    const result = await exchangeRateService.convertCurrency(amount, fromCurrency, toCurrency, date);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};
