'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Hotel tablosunu kaldırdığımız için, hotel_id ve secondary_hotel_id sütunlarını kaldırıp
    // hotel_name ve secondary_hotel_name sütunlarını ekleyelim
    
    // Önce hotel_id ve secondary_hotel_id sütunlarını kaldıralım
    await queryInterface.removeColumn('reservations', 'hotel_id');
    await queryInterface.removeColumn('reservations', 'secondary_hotel_id');
    
    // Şimdi hotel_name ve secondary_hotel_name sütunlarını ekleyelim
    await queryInterface.addColumn('reservations', 'hotel_name', {
      type: Sequelize.STRING(100),
      allowNull: true
    });
    
    await queryInterface.addColumn('reservations', 'secondary_hotel_name', {
      type: Sequelize.STRING(100),
      allowNull: true
    });
    
    // Agencies tablosundan agency_code sütununu kaldıralım
    await queryInterface.removeColumn('agencies', 'agency_code');
  },

  down: async (queryInterface, Sequelize) => {
    // Geri alma işlemi için, eklediğimiz sütunları kaldırıp, kaldırdığımız sütunları geri ekleyelim
    
    // Eklediğimiz sütunları kaldıralım
    await queryInterface.removeColumn('reservations', 'hotel_name');
    await queryInterface.removeColumn('reservations', 'secondary_hotel_name');
    
    // Kaldırdığımız sütunları geri ekleyelim
    await queryInterface.addColumn('reservations', 'hotel_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'hotels',
        key: 'id'
      }
    });
    
    await queryInterface.addColumn('reservations', 'secondary_hotel_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'hotels',
        key: 'id'
      }
    });
    
    // Agencies tablosuna agency_code sütununu geri ekleyelim
    await queryInterface.addColumn('agencies', 'agency_code', {
      type: Sequelize.STRING(20),
      allowNull: false,
      unique: true
    });
  }
};
