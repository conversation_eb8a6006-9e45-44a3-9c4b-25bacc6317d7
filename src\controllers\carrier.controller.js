// server/controllers/carrier.controller.js

const carrierService = require('../services/carrier.service');
const accountService = require('../services/account.service');

/**
 * Tüm taşımacıları getir
 * @route GET /api/carriers
 */
exports.getAllCarriers = async (req, res, next) => {
  try {
    const {
      name,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      name,
      isActive,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await carrierService.getAllCarriers(filters);

    res.status(200).json({
      success: true,
      data: {
        rows: result.rows,
        count: result.count
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacı detaylarını getir
 * @route GET /api/carriers/:id
 */
exports.getCarrierById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const carrier = await carrierService.getCarrierById(id);

    res.status(200).json({
      success: true,
      data: carrier
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni taşımacı oluştur
 * @route POST /api/carriers
 */
exports.createCarrier = async (req, res, next) => {
  try {
    const {
      name,
      email,
      phone,
      address,
      contactPerson,
      carrierCode,
      isActive
    } = req.body;

    const newCarrier = await carrierService.createCarrier({
      name,
      email,
      phone,
      address,
      contactPerson,
      carrierCode,
      isActive
    });

    res.status(201).json({
      success: true,
      data: newCarrier,
      message: 'Taşımacı başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacı güncelle
 * @route PUT /api/carriers/:id
 */
exports.updateCarrier = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      address,
      contactPerson,
      carrierCode,
      isActive
    } = req.body;

    const updatedCarrier = await carrierService.updateCarrier(id, {
      name,
      email,
      phone,
      address,
      contactPerson,
      carrierCode,
      isActive
    });

    res.status(200).json({
      success: true,
      data: updatedCarrier,
      message: 'Taşımacı başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacı sil
 * @route DELETE /api/carriers/:id
 */
exports.deleteCarrier = async (req, res, next) => {
  try {
    const { id } = req.params;

    await carrierService.deleteCarrier(id);

    res.status(200).json({
      success: true,
      message: 'Taşımacı başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacıya ait araçları getir
 * @route GET /api/carriers/:id/vehicles
 */
exports.getCarrierVehicles = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      isActive,
      vehicleTypeId,
      page = 1,
      limit = 20
    } = req.query;

    const filters = {
      carrierId: id,
      isActive,
      vehicleTypeId,
      page,
      limit
    };

    const vehicles = await carrierService.getCarrierVehicles(filters);

    res.status(200).json({
      success: true,
      data: {
        rows: vehicles.rows,
        count: vehicles.count
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacı hesap bilgilerini getir
 * @route GET /api/carriers/:id/account
 */
exports.getCarrierAccount = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { currency } = req.query;

    const account = await accountService.getAccountByEntity('carrier', id, currency);

    res.status(200).json({
      success: true,
      data: account
    });
  } catch (error) {
    next(error);
  }
};
