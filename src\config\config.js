module.exports = {
  development: {
    port: process.env.PORT || 3000,
    jwtSecret: process.env.JWT_SECRET || 'developmentsecret',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
    db: {
      database: process.env.DB_NAME || 'operasyon_sistemi',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
    },
    email: {
      host: process.env.EMAIL_HOST || 'smtp.example.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      user: process.env.EMAIL_USER || '<EMAIL>',
      password: process.env.EMAIL_PASSWORD || 'password',
      fromName: process.env.EMAIL_FROM_NAME || 'Operasyon Sistemi',
      fromEmail: process.env.EMAIL_FROM_EMAIL || '<EMAIL>'
    }
  },
  production: {
    port: process.env.PORT,
    jwtSecret: process.env.JWT_SECRET,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
    db: {
      database: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
    },
    email: {
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      user: process.env.EMAIL_USER,
      password: process.env.EMAIL_PASSWORD,
      fromName: process.env.EMAIL_FROM_NAME,
      fromEmail: process.env.EMAIL_FROM_EMAIL
    }
  },
  test: {
    port: 3001,
    jwtSecret: 'testsecret',
    jwtExpiresIn: '1d',
    db: {
      database: 'operasyon_sistemi_test',
      user: 'root',
      password: '',
      host: 'localhost',
      port: 3306,
    }
  }
};

// Export config based on environment
const env = process.env.NODE_ENV || 'development';
module.exports = module.exports[env];