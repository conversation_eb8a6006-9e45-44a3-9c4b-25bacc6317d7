// server/controllers/vehicle.controller.js

const vehicleService = require('../services/vehicle.service');

/**
 * Tüm araçları getir
 * @route GET /api/vehicles
 */
exports.getAllVehicles = async (req, res, next) => {
  try {
    const {
      plateNumber,
      carrierId,
      vehicleTypeId,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'plate_number',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      plateNumber,
      carrierId,
      vehicleTypeId,
      isActive,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await vehicleService.getAllVehicles(filters);

    res.status(200).json({
      success: true,
      data: {
        rows: result.rows,
        count: result.count,
        pagination: result.pagination
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç detaylarını getir
 * @route GET /api/vehicles/:id
 */
exports.getVehicleById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const vehicle = await vehicleService.getVehicleById(id);

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni araç oluştur
 * @route POST /api/vehicles
 */
exports.createVehicle = async (req, res, next) => {
  try {
    const {
      plateNumber,
      vehicleTypeId,
      carrierId,
      driverName,
      driverPhone,
      isActive
    } = req.body;

    const newVehicle = await vehicleService.createVehicle({
      plateNumber,
      vehicleTypeId,
      carrierId,
      driverName,
      driverPhone,
      isActive
    });

    res.status(201).json({
      success: true,
      data: newVehicle,
      message: 'Araç başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç güncelle
 * @route PUT /api/vehicles/:id
 */
exports.updateVehicle = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      plateNumber,
      vehicleTypeId,
      carrierId,
      driverName,
      driverPhone,
      isActive,
      driverRating
    } = req.body;

    const updatedVehicle = await vehicleService.updateVehicle(id, {
      plateNumber,
      vehicleTypeId,
      carrierId,
      driverName,
      driverPhone,
      isActive,
      driverRating
    });

    res.status(200).json({
      success: true,
      data: updatedVehicle,
      message: 'Araç başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç sil
 * @route DELETE /api/vehicles/:id
 */
exports.deleteVehicle = async (req, res, next) => {
  try {
    const { id } = req.params;

    await vehicleService.deleteVehicle(id);

    res.status(200).json({
      success: true,
      message: 'Araç başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Taşımacıya ait araçları getir
 * @route GET /api/vehicles/carrier/:carrierId
 */
exports.getVehiclesByCarrier = async (req, res, next) => {
  try {
    const { carrierId } = req.params;
    const {
      isActive,
      vehicleTypeId,
      page = 1,
      limit = 20
    } = req.query;

    const filters = {
      carrierId,
      isActive,
      vehicleTypeId,
      page,
      limit
    };

    const result = await vehicleService.getVehiclesByCarrier(filters);

    res.status(200).json({
      success: true,
      data: {
        rows: result.rows,
        count: result.count,
        pagination: result.pagination
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Tipe göre araçları getir
 * @route GET /api/vehicles/type/:typeId
 */
exports.getVehiclesByType = async (req, res, next) => {
  try {
    const { typeId } = req.params;
    const {
      isActive,
      carrierId,
      page = 1,
      limit = 20
    } = req.query;

    const filters = {
      vehicleTypeId: typeId,
      isActive,
      carrierId,
      page,
      limit
    };

    const result = await vehicleService.getVehiclesByType(filters);

    res.status(200).json({
      success: true,
      data: {
        rows: result.rows,
        count: result.count,
        pagination: result.pagination
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Araç fotoğrafı yükle
 * @route POST /api/vehicles/:id/photos
 */
exports.uploadVehiclePhoto = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Lütfen bir fotoğraf yükleyin'
      });
    }

    const result = await vehicleService.uploadVehiclePhoto(id, req.file);

    res.status(200).json({
      success: true,
      data: result,
      message: 'Araç fotoğrafı başarıyla yüklendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Sürücü fotoğrafı yükle
 * @route POST /api/vehicles/:id/driver-photo
 */
exports.uploadDriverPhoto = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Lütfen bir fotoğraf yükleyin'
      });
    }

    const result = await vehicleService.uploadDriverPhoto(id, req.file);

    res.status(200).json({
      success: true,
      data: result,
      message: 'Sürücü fotoğrafı başarıyla yüklendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belirli tarih ve saatte müsait araçları getir
 * @route GET /api/vehicles/available
 */
exports.getAvailableVehicles = async (req, res, next) => {
  try {
    const {
      date,
      time,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    } = req.query;

    if (!date || !time) {
      return res.status(400).json({
        success: false,
        message: 'Tarih ve saat bilgileri gereklidir'
      });
    }

    const availableVehicles = await vehicleService.getAvailableVehicles({
      date,
      time,
      vehicleTypeId,
      fromRegionId,
      toRegionId
    });

    res.status(200).json({
      success: true,
      data: availableVehicles
    });
  } catch (error) {
    next(error);
  }
};
