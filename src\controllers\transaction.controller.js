// server/controllers/transaction.controller.js

const transactionService = require('../services/transaction.service');

/**
 * Tüm işlemleri getir
 * @route GET /api/transactions
 */
exports.getAllTransactions = async (req, res, next) => {
  try {
    const { 
      accountId, 
      transactionType, 
      startDate, 
      endDate,
      reservationId,
      page = 1, 
      limit = 20,
      sortBy = 'transaction_date',
      sortDir = 'DESC'
    } = req.query;

    const filters = {
      accountId,
      transactionType,
      startDate,
      endDate,
      reservationId,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await transactionService.getAllTransactions(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İşlem detaylarını getir
 * @route GET /api/transactions/:id
 */
exports.getTransactionById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const transaction = await transactionService.getTransactionById(id);
    
    res.status(200).json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni işlem oluştur
 * @route POST /api/transactions
 */
exports.createTransaction = async (req, res, next) => {
  try {
    const { 
      accountId, 
      amount, 
      currency, 
      transactionType, 
      description, 
      referenceNumber, 
      reservationId 
    } = req.body;
    
    const newTransaction = await transactionService.createTransaction({
      accountId,
      amount,
      currency,
      transactionType,
      description,
      referenceNumber,
      reservationId,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: newTransaction,
      message: 'İşlem başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İşlem güncelle
 * @route PUT /api/transactions/:id
 */
exports.updateTransaction = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      description, 
      referenceNumber 
    } = req.body;
    
    const updatedTransaction = await transactionService.updateTransaction(id, {
      description,
      referenceNumber
    });
    
    res.status(200).json({
      success: true,
      data: updatedTransaction,
      message: 'İşlem başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İşlem sil
 * @route DELETE /api/transactions/:id
 */
exports.deleteTransaction = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await transactionService.deleteTransaction(id);
    
    res.status(200).json({
      success: true,
      message: 'İşlem başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Rezervasyona ait işlemleri getir
 * @route GET /api/transactions/reservation/:reservationId
 */
exports.getTransactionsByReservation = async (req, res, next) => {
  try {
    const { reservationId } = req.params;
    
    const transactions = await transactionService.getTransactionsByReservation(reservationId);
    
    res.status(200).json({
      success: true,
      data: transactions
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Ödeme işlemi oluştur
 * @route POST /api/transactions/payment
 */
exports.createPayment = async (req, res, next) => {
  try {
    const { 
      accountId, 
      amount, 
      currency, 
      description, 
      referenceNumber, 
      reservationId 
    } = req.body;
    
    const payment = await transactionService.createPayment({
      accountId,
      amount,
      currency,
      description,
      referenceNumber,
      reservationId,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: payment,
      message: 'Ödeme başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İade işlemi oluştur
 * @route POST /api/transactions/refund
 */
exports.createRefund = async (req, res, next) => {
  try {
    const { 
      accountId, 
      amount, 
      currency, 
      description, 
      referenceNumber, 
      reservationId 
    } = req.body;
    
    const refund = await transactionService.createRefund({
      accountId,
      amount,
      currency,
      description,
      referenceNumber,
      reservationId,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: refund,
      message: 'İade başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};
