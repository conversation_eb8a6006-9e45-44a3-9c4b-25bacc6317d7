// server/middleware/auth.js

const jwt = require('jsonwebtoken');
const { User, Permission } = require('../models');
const config = require('../config/config');
const AppError = require('../utils/appError');

/**
 * Kimlik doğrulama middleware'i
 * @param {Object} req - Request nesnesi
 * @param {Object} res - Response nesnesi
 * @param {Function} next - Next fonksiyonu
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Token'ı al
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return next(new AppError('<PERSON><PERSON><PERSON> yapılmamış', 401));
    }

    // Token'ı doğrula
    const decoded = jwt.verify(token, config.jwtSecret);

    // Kullanıcıyı bul
    const user = await User.findByPk(decoded.id, {
      include: [{ model: Permission, as: 'permissions' }],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return next(new AppError('Bu token ile ilişkili kullanıcı artık mevcut değil', 401));
    }

    // Kullanıcı aktif değilse
    if (!user.is_active) {
      return next(new AppError('Hesap devre dışı bırakılmış', 401));
    }

    // Kullanıcıyı request'e ekle
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Geçersiz token', 401));
    }
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token süresi dolmuş', 401));
    }
    next(error);
  }
};

/**
 * Rol bazlı yetkilendirme middleware'i
 * @param {Array} roles - İzin verilen roller
 * @returns {Function} Middleware fonksiyonu
 */
exports.authorize = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Giriş yapılmamış', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AppError('Bu işlemi gerçekleştirmek için yetkiniz yok', 403));
    }

    next();
  };
};

/**
 * İzin kontrolü middleware'i
 * @param {string} permission - Kontrol edilecek izin
 * @returns {Function} Middleware fonksiyonu
 */
exports.checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Giriş yapılmamış', 401));
    }

    // Admin her zaman tüm izinlere sahiptir
    if (req.user.role === 'admin') {
      return next();
    }

    // Kullanıcının izinlerini kontrol et
    const userPermissions = req.user.permissions;
    if (!userPermissions || !userPermissions[permission]) {
      return next(new AppError('Bu işlemi gerçekleştirmek için yetkiniz yok', 403));
    }

    next();
  };
};