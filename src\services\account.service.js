// server/services/account.service.js

const { 
  Account, 
  Transaction, 
  Agency, 
  Carrier, 
  User, 
  Reservation,
  sequelize 
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');
const PDFDocument = require('pdfkit');

/**
 * Tüm hesapları listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Hesap listesi ve sayfalama bilgileri
 */
exports.getAllAccounts = async (filters) => {
  const { 
    entityType, 
    entityId, 
    currency,
    page = 1, 
    limit = 20,
    sortBy = 'id',
    sortDir = 'ASC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {};
  
  if (entityType) {
    whereConditions.entity_type = entityType;
  }
  
  if (entityId) {
    whereConditions.entity_id = entityId;
  }
  
  if (currency) {
    whereConditions.currency = currency;
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Account.findAndCountAll({
    where: whereConditions,
    order,
    limit,
    offset
  });

  // İlişkili varlıkları yükle
  for (const account of rows) {
    if (account.entity_type === 'agency') {
      account.dataValues.entity = await Agency.findByPk(account.entity_id);
    } else if (account.entity_type === 'carrier') {
      account.dataValues.entity = await Carrier.findByPk(account.entity_id);
    }
  }

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    accounts: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Hesap detaylarını getirme servisi
 * @param {number} id - Hesap ID
 * @returns {Object} Hesap detayları
 */
exports.getAccountById = async (id) => {
  const account = await Account.findByPk(id);
  
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }
  
  // İlişkili varlığı yükle
  if (account.entity_type === 'agency') {
    account.dataValues.entity = await Agency.findByPk(account.entity_id);
  } else if (account.entity_type === 'carrier') {
    account.dataValues.entity = await Carrier.findByPk(account.entity_id);
  }
  
  return account;
};

/**
 * Belirli bir varlığa ait hesabı getirme servisi
 * @param {string} type - Varlık tipi (agency/carrier)
 * @param {number} id - Varlık ID
 * @param {string} currency - Para birimi
 * @returns {Object} Hesap detayları
 */
exports.getAccountByEntity = async (type, id, currency) => {
  // Varlık kontrolü
  if (type === 'agency') {
    const agency = await Agency.findByPk(id);
    if (!agency) {
      throw new AppError('Acente bulunamadı', 404);
    }
  } else if (type === 'carrier') {
    const carrier = await Carrier.findByPk(id);
    if (!carrier) {
      throw new AppError('Taşımacı bulunamadı', 404);
    }
  } else {
    throw new AppError('Geçersiz varlık tipi', 400);
  }

  // Hesap sorgusu
  const whereConditions = {
    entity_type: type,
    entity_id: id
  };
  
  if (currency) {
    whereConditions.currency = currency;
  }
  
  const account = await Account.findOne({
    where: whereConditions
  });
  
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }
  
  // İlişkili varlığı yükle
  if (account.entity_type === 'agency') {
    account.dataValues.entity = await Agency.findByPk(account.entity_id);
  } else if (account.entity_type === 'carrier') {
    account.dataValues.entity = await Carrier.findByPk(account.entity_id);
  }
  
  return account;
};

/**
 * Yeni hesap oluşturma servisi
 * @param {Object} accountData - Hesap verileri
 * @returns {Object} Oluşturulan hesap
 */
exports.createAccount = async (accountData) => {
  const { entityType, entityId, balance = 0, currency = 'EUR' } = accountData;

  // Varlık kontrolü
  if (entityType === 'agency') {
    const agency = await Agency.findByPk(entityId);
    if (!agency) {
      throw new AppError('Acente bulunamadı', 404);
    }
  } else if (entityType === 'carrier') {
    const carrier = await Carrier.findByPk(entityId);
    if (!carrier) {
      throw new AppError('Taşımacı bulunamadı', 404);
    }
  } else {
    throw new AppError('Geçersiz varlık tipi', 400);
  }

  // Aynı varlık ve para birimi için hesap var mı kontrol et
  const existingAccount = await Account.findOne({
    where: {
      entity_type: entityType,
      entity_id: entityId,
      currency
    }
  });

  if (existingAccount) {
    throw new AppError('Bu varlık ve para birimi için hesap zaten mevcut', 400);
  }

  // Yeni hesap oluştur
  const newAccount = await Account.create({
    entity_type: entityType,
    entity_id: entityId,
    balance,
    currency,
    last_updated: new Date()
  });

  return newAccount;
};

/**
 * Hesap güncelleme servisi
 * @param {number} id - Hesap ID
 * @param {Object} accountData - Güncellenecek veriler
 * @returns {Object} Güncellenen hesap
 */
exports.updateAccount = async (id, accountData) => {
  const { balance, currency } = accountData;

  // Hesabı bul
  const account = await Account.findByPk(id);
  
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }

  // Para birimi değiştiyse, aynı varlık için bu para biriminde hesap var mı kontrol et
  if (currency && currency !== account.currency) {
    const existingAccount = await Account.findOne({
      where: {
        entity_type: account.entity_type,
        entity_id: account.entity_id,
        currency,
        id: { [Op.ne]: id }
      }
    });

    if (existingAccount) {
      throw new AppError('Bu varlık ve para birimi için hesap zaten mevcut', 400);
    }
  }

  // Güncelle
  await account.update({
    balance: balance !== undefined ? balance : account.balance,
    currency: currency || account.currency,
    last_updated: new Date()
  });

  return account;
};

/**
 * Hesap işlemlerini getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} İşlem listesi ve sayfalama bilgileri
 */
exports.getAccountTransactions = async (filters) => {
  const { 
    accountId, 
    startDate, 
    endDate, 
    transactionType,
    page = 1, 
    limit = 20,
    sortBy = 'transaction_date',
    sortDir = 'DESC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {
    account_id: accountId
  };
  
  if (startDate && endDate) {
    whereConditions.transaction_date = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    whereConditions.transaction_date = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    whereConditions.transaction_date = {
      [Op.lte]: new Date(endDate)
    };
  }
  
  if (transactionType) {
    whereConditions.transaction_type = transactionType;
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Transaction.findAndCountAll({
    where: whereConditions,
    include: [
      { model: User, as: 'creator' },
      { model: Reservation, as: 'Reservation' }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    transactions: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Hesap ekstresi oluşturma servisi
 * @param {number} id - Hesap ID
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @param {string} format - Çıktı formatı (pdf/json)
 * @returns {Buffer|Object} Hesap ekstresi
 */
exports.generateAccountStatement = async (id, startDate, endDate, format = 'pdf') => {
  // Hesabı bul
  const account = await Account.findByPk(id);
  
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }
  
  // İlişkili varlığı yükle
  let entityName = '';
  if (account.entity_type === 'agency') {
    const agency = await Agency.findByPk(account.entity_id);
    entityName = agency ? agency.name : 'Bilinmeyen Acente';
  } else if (account.entity_type === 'carrier') {
    const carrier = await Carrier.findByPk(account.entity_id);
    entityName = carrier ? carrier.name : 'Bilinmeyen Taşımacı';
  }
  
  // Tarih aralığı
  const startDateTime = startDate ? new Date(startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
  const endDateTime = endDate ? new Date(endDate) : new Date();
  
  // İşlemleri getir
  const transactions = await Transaction.findAll({
    where: {
      account_id: id,
      transaction_date: {
        [Op.between]: [startDateTime, endDateTime]
      }
    },
    include: [
      { model: User, as: 'creator' },
      { model: Reservation, as: 'Reservation' }
    ],
    order: [['transaction_date', 'ASC']]
  });
  
  // Başlangıç bakiyesini hesapla
  const previousTransactions = await Transaction.findAll({
    where: {
      account_id: id,
      transaction_date: {
        [Op.lt]: startDateTime
      }
    },
    attributes: [
      [sequelize.fn('SUM', 
        sequelize.literal(`CASE WHEN transaction_type IN ('payment', 'refund') THEN amount ELSE -amount END`)
      ), 'balance']
    ],
    raw: true
  });
  
  const startingBalance = previousTransactions[0].balance || 0;
  
  // İşlem özetini hazırla
  let runningBalance = parseFloat(startingBalance);
  const transactionSummary = transactions.map(transaction => {
    const amount = transaction.transaction_type === 'payment' || transaction.transaction_type === 'refund' 
      ? parseFloat(transaction.amount) 
      : -parseFloat(transaction.amount);
    
    runningBalance += amount;
    
    return {
      id: transaction.id,
      date: transaction.transaction_date,
      type: transaction.transaction_type,
      description: transaction.description,
      reference: transaction.reference_number,
      reservation: transaction.Reservation ? transaction.Reservation.reservation_number : null,
      amount: transaction.amount,
      currency: transaction.currency,
      balance: runningBalance,
      createdBy: transaction.creator ? transaction.creator.full_name : 'Sistem'
    };
  });
  
  // JSON formatı için
  if (format === 'json') {
    return {
      account: {
        id: account.id,
        entityType: account.entity_type,
        entityId: account.entity_id,
        entityName,
        currency: account.currency,
        currentBalance: account.balance
      },
      statement: {
        startDate: startDateTime,
        endDate: endDateTime,
        startingBalance,
        endingBalance: runningBalance,
        transactions: transactionSummary
      }
    };
  }
  
  // PDF formatı için
  const doc = new PDFDocument({ margin: 50 });
  
  // Başlık
  doc.fontSize(20).text('Hesap Ekstresi', { align: 'center' });
  doc.moveDown();
  
  // Hesap bilgileri
  doc.fontSize(12).text(`Hesap No: ${account.id}`);
  doc.text(`Varlık: ${entityName} (${account.entity_type === 'agency' ? 'Acente' : 'Taşımacı'})`);
  doc.text(`Para Birimi: ${account.currency}`);
  doc.text(`Dönem: ${startDateTime.toLocaleDateString()} - ${endDateTime.toLocaleDateString()}`);
  doc.text(`Başlangıç Bakiyesi: ${startingBalance} ${account.currency}`);
  doc.text(`Güncel Bakiye: ${account.balance} ${account.currency}`);
  doc.moveDown();
  
  // İşlem tablosu
  doc.fontSize(10);
  const tableTop = 200;
  const tableLeft = 50;
  
  // Tablo başlıkları
  doc.text('Tarih', tableLeft, tableTop);
  doc.text('İşlem Tipi', tableLeft + 80, tableTop);
  doc.text('Açıklama', tableLeft + 160, tableTop);
  doc.text('Tutar', tableLeft + 300, tableTop);
  doc.text('Bakiye', tableLeft + 360, tableTop);
  
  doc.moveTo(tableLeft, tableTop + 15).lineTo(tableLeft + 450, tableTop + 15).stroke();
  
  // Tablo içeriği
  let rowTop = tableTop + 30;
  
  transactionSummary.forEach(transaction => {
    // Sayfa sınırını kontrol et
    if (rowTop > 700) {
      doc.addPage();
      rowTop = 50;
    }
    
    doc.text(transaction.date.toLocaleDateString(), tableLeft, rowTop);
    
    let typeText = '';
    switch(transaction.type) {
      case 'payment':
        typeText = 'Ödeme';
        break;
      case 'charge':
        typeText = 'Tahsilat';
        break;
      case 'refund':
        typeText = 'İade';
        break;
      case 'adjustment':
        typeText = 'Düzeltme';
        break;
      default:
        typeText = transaction.type;
    }
    
    doc.text(typeText, tableLeft + 80, rowTop);
    
    // Açıklama metni uzunsa kısalt
    const description = transaction.description 
      ? (transaction.description.length > 25 
          ? transaction.description.substring(0, 22) + '...' 
          : transaction.description)
      : '';
    
    doc.text(description, tableLeft + 160, rowTop);
    
    const amountText = transaction.type === 'payment' || transaction.type === 'refund'
      ? `+${transaction.amount}`
      : `-${transaction.amount}`;
    
    doc.text(`${amountText} ${transaction.currency}`, tableLeft + 300, rowTop);
    doc.text(`${transaction.balance.toFixed(2)} ${transaction.currency}`, tableLeft + 360, rowTop);
    
    rowTop += 20;
  });
  
  // PDF'i tamamla
  doc.end();
  
  return doc;
};
