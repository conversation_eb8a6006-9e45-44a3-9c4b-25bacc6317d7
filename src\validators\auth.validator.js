// server/validators/auth.validator.js

const Joi = require('joi');

// Kullanıcı girişi şeması
const login = Joi.object({
  username: Joi.string().required().messages({
    'string.empty': '<PERSON>llan<PERSON>c<PERSON> adı boş olamaz',
    'any.required': '<PERSON>llanıcı adı gereklidir'
  }),
  password: Joi.string().required().messages({
    'string.empty': 'Şifre boş olamaz',
    'any.required': '<PERSON><PERSON>re gereklidir'
  })
});

// Şifre değiştirme şeması
const changePassword = Joi.object({
  currentPassword: Joi.string().required().messages({
    'string.empty': 'Mevcut şifre boş olamaz',
    'any.required': 'Mevcut şifre gereklidir'
  }),
  newPassword: Joi.string().min(6).required().messages({
    'string.empty': 'Yeni şifre boş olamaz',
    'string.min': 'Yeni şifre en az {#limit} karakter olmalıdır',
    'any.required': 'Yeni şifre gereklidir'
  }),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
    'string.empty': 'Şifre onayı boş olamaz',
    'any.only': 'Şifreler eşleşmiyor',
    'any.required': 'Şifre onayı gereklidir'
  })
});

// Kullanıcı kaydı şeması
const register = Joi.object({
  username: Joi.string().min(3).max(50).required().messages({
    'string.empty': 'Kullanıcı adı boş olamaz',
    'string.min': 'Kullanıcı adı en az {#limit} karakter olmalıdır',
    'string.max': 'Kullanıcı adı en fazla {#limit} karakter olmalıdır',
    'any.required': 'Kullanıcı adı gereklidir'
  }),
  password: Joi.string().min(6).required().messages({
    'string.empty': 'Şifre boş olamaz',
    'string.min': 'Şifre en az {#limit} karakter olmalıdır',
    'any.required': 'Şifre gereklidir'
  }),
  email: Joi.string().email().required().messages({
    'string.empty': 'E-posta boş olamaz',
    'string.email': 'Geçerli bir e-posta adresi giriniz',
    'any.required': 'E-posta gereklidir'
  }),
  fullName: Joi.string().required().messages({
    'string.empty': 'Ad Soyad boş olamaz',
    'any.required': 'Ad Soyad gereklidir'
  }),
  phone: Joi.string().allow('').optional(),
  role: Joi.string().valid('admin', 'manager', 'operation', 'finance', 'agent', 'driver').required().messages({
    'string.empty': 'Rol boş olamaz',
    'any.only': 'Geçersiz rol',
    'any.required': 'Rol gereklidir'
  }),
  isActive: Joi.boolean().default(true),
  permissions: Joi.object({
    canAccessData: Joi.boolean().default(false),
    canManagePrices: Joi.boolean().default(false),
    canManageReservations: Joi.boolean().default(false),
    canManageOperations: Joi.boolean().default(false),
    canManagePayments: Joi.boolean().default(false)
  }).default({})
});

// Şifremi unuttum şeması
const forgotPassword = Joi.object({
  email: Joi.string().email().required().messages({
    'string.empty': 'E-posta boş olamaz',
    'string.email': 'Geçerli bir e-posta adresi giriniz',
    'any.required': 'E-posta gereklidir'
  })
});

// Şifre sıfırlama şeması
const resetPassword = Joi.object({
  token: Joi.string().required().messages({
    'string.empty': 'Token boş olamaz',
    'any.required': 'Token gereklidir'
  }),
  newPassword: Joi.string().min(6).required().messages({
    'string.empty': 'Yeni şifre boş olamaz',
    'string.min': 'Yeni şifre en az {#limit} karakter olmalıdır',
    'any.required': 'Yeni şifre gereklidir'
  }),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
    'string.empty': 'Şifre onayı boş olamaz',
    'any.only': 'Şifreler eşleşmiyor',
    'any.required': 'Şifre onayı gereklidir'
  })
});

module.exports = {
  authSchema: {
    login,
    changePassword,
    register,
    forgotPassword,
    resetPassword
  }
};
