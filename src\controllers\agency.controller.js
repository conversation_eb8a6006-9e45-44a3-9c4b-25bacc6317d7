// server/controllers/agency.controller.js

const agencyService = require('../services/agency.service');

/**
 * Tüm acenteleri getir
 * @route GET /api/agencies
 */
exports.getAllAgencies = async (req, res, next) => {
  try {
    const {
      name,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      name,
      isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sortBy,
      sortDir
    };

    const result = await agencyService.getAllAgencies(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente detaylarını getir
 * @route GET /api/agencies/:id
 */
exports.getAgencyById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const agency = await agencyService.getAgencyById(id);

    res.status(200).json({
      success: true,
      agency
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni acente oluştur
 * @route POST /api/agencies
 */
exports.createAgency = async (req, res, next) => {
  try {
    const agencyData = req.body;
    const userId = req.user.id;

    const agency = await agencyService.createAgency(agencyData, userId);

    res.status(201).json({
      success: true,
      message: 'Acente başarıyla oluşturuldu',
      agency
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente güncelleme
 * @route PUT /api/agencies/:id
 */
exports.updateAgency = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user.id;

    const agency = await agencyService.updateAgency(id, updateData, userId);

    res.status(200).json({
      success: true,
      message: 'Acente başarıyla güncellendi',
      agency
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente durumunu değiştir (aktif/pasif)
 * @route PATCH /api/agencies/:id/status
 */
exports.toggleAgencyStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;
    const userId = req.user.id;

    const agency = await agencyService.toggleAgencyStatus(id, isActive, userId);

    res.status(200).json({
      success: true,
      message: `Acente durumu ${isActive ? 'aktif' : 'pasif'} olarak güncellendi`,
      agency
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente fiyatlarını getir
 * @route GET /api/agencies/:id/prices
 */
exports.getAgencyPrices = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page = 1,
      limit = 50
    } = req.query;

    const filters = {
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page,
      limit
    };

    const result = await agencyService.getAgencyPrices(id, filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente fiyatı oluştur
 * @route POST /api/agencies/:id/prices
 */
exports.createAgencyPrice = async (req, res, next) => {
  try {
    const { id } = req.params;
    const priceData = req.body;
    const userId = req.user.id;

    const price = await agencyService.createAgencyPrice(id, priceData, userId);

    res.status(201).json({
      success: true,
      message: 'Acente fiyatı başarıyla oluşturuldu',
      price
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente fiyatı güncelleme
 * @route PUT /api/agencies/:agencyId/prices/:priceId
 */
exports.updateAgencyPrice = async (req, res, next) => {
  try {
    const { agencyId, priceId } = req.params;
    const updateData = req.body;
    const userId = req.user.id;

    const price = await agencyService.updateAgencyPrice(agencyId, priceId, updateData, userId);

    res.status(200).json({
      success: true,
      message: 'Acente fiyatı başarıyla güncellendi',
      price
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente fiyatı silme
 * @route DELETE /api/agencies/:agencyId/prices/:priceId
 */
exports.deleteAgencyPrice = async (req, res, next) => {
  try {
    const { agencyId, priceId } = req.params;
    const userId = req.user.id;

    await agencyService.deleteAgencyPrice(agencyId, priceId, userId);

    res.status(200).json({
      success: true,
      message: 'Acente fiyatı başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente kullanıcı listesi
 * @route GET /api/agencies/:id/users
 */
exports.getAgencyUsers = async (req, res, next) => {
  try {
    const { id } = req.params;

    const users = await agencyService.getAgencyUsers(id);

    res.status(200).json({
      success: true,
      users
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente kullanıcısı oluştur
 * @route POST /api/agencies/:id/users
 */
exports.createAgencyUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userData = req.body;
    const adminId = req.user.id;

    const user = await agencyService.createAgencyUser(id, userData, adminId);

    res.status(201).json({
      success: true,
      message: 'Acente kullanıcısı başarıyla oluşturuldu',
      user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente silme
 * @route DELETE /api/agencies/:id
 */
exports.deleteAgency = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    await agencyService.deleteAgency(id, userId);

    res.status(200).json({
      success: true,
      message: 'Acente başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acenteye ait rezervasyonları getir
 * @route GET /api/agencies/:id/reservations
 */
exports.getAgencyReservations = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      status,
      startDate,
      endDate,
      page = 1,
      limit = 20
    } = req.query;

    const filters = {
      status,
      startDate,
      endDate,
      page,
      limit
    };

    const result = await agencyService.getAgencyReservations(id, filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Acente hesap bilgilerini getir
 * @route GET /api/agencies/:id/account
 */
exports.getAgencyAccount = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { currency } = req.query;

    const account = await agencyService.getAgencyAccount(id, currency);

    res.status(200).json({
      success: true,
      data: account
    });
  } catch (error) {
    next(error);
  }
};