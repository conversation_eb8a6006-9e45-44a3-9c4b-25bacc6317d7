// server/services/transaction.service.js

const { 
  Transaction, 
  Account, 
  User, 
  Reservation,
  Agency,
  Carrier,
  sequelize 
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm işlemleri listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} İşlem listesi ve sayfalama bilgileri
 */
exports.getAllTransactions = async (filters) => {
  const { 
    accountId, 
    transactionType, 
    startDate, 
    endDate,
    reservationId,
    page = 1, 
    limit = 20,
    sortBy = 'transaction_date',
    sortDir = 'DESC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {};
  
  if (accountId) {
    whereConditions.account_id = accountId;
  }
  
  if (transactionType) {
    whereConditions.transaction_type = transactionType;
  }
  
  if (reservationId) {
    whereConditions.reservation_id = reservationId;
  }
  
  if (startDate && endDate) {
    whereConditions.transaction_date = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    whereConditions.transaction_date = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    whereConditions.transaction_date = {
      [Op.lte]: new Date(endDate)
    };
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Transaction.findAndCountAll({
    where: whereConditions,
    include: [
      { 
        model: Account,
        include: [
          { model: Agency, as: 'Agency' },
          { model: Carrier, as: 'Carrier' }
        ]
      },
      { model: User, as: 'creator' },
      { model: Reservation, as: 'Reservation' }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    transactions: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * İşlem detaylarını getirme servisi
 * @param {number} id - İşlem ID
 * @returns {Object} İşlem detayları
 */
exports.getTransactionById = async (id) => {
  const transaction = await Transaction.findByPk(id, {
    include: [
      { 
        model: Account,
        include: [
          { model: Agency, as: 'Agency' },
          { model: Carrier, as: 'Carrier' }
        ]
      },
      { model: User, as: 'creator' },
      { model: Reservation, as: 'Reservation' }
    ]
  });
  
  if (!transaction) {
    throw new AppError('İşlem bulunamadı', 404);
  }
  
  return transaction;
};

/**
 * Yeni işlem oluşturma servisi
 * @param {Object} transactionData - İşlem verileri
 * @returns {Object} Oluşturulan işlem
 */
exports.createTransaction = async (transactionData) => {
  const { 
    accountId, 
    amount, 
    currency, 
    transactionType, 
    description, 
    referenceNumber, 
    reservationId,
    createdBy
  } = transactionData;

  // Hesap kontrolü
  const account = await Account.findByPk(accountId);
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }
  
  // Para birimi kontrolü
  if (currency !== account.currency) {
    throw new AppError(`İşlem para birimi (${currency}) hesap para birimi (${account.currency}) ile uyuşmuyor`, 400);
  }
  
  // Rezervasyon kontrolü (eğer belirtildiyse)
  if (reservationId) {
    const reservation = await Reservation.findByPk(reservationId);
    if (!reservation) {
      throw new AppError('Rezervasyon bulunamadı', 404);
    }
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();
  
  try {
    // Yeni işlem oluştur
    const newTransaction = await Transaction.create({
      account_id: accountId,
      amount,
      currency,
      transaction_type: transactionType,
      description,
      reference_number: referenceNumber,
      reservation_id: reservationId,
      transaction_date: new Date(),
      created_by: createdBy
    }, { transaction });
    
    // Hesap bakiyesini güncelle
    let balanceChange = 0;
    
    if (transactionType === 'payment' || transactionType === 'refund') {
      // Ödeme veya iade: bakiyeyi artır
      balanceChange = parseFloat(amount);
    } else if (transactionType === 'charge' || transactionType === 'adjustment') {
      // Tahsilat veya düzeltme: bakiyeyi azalt
      balanceChange = -parseFloat(amount);
    }
    
    await account.update({
      balance: parseFloat(account.balance) + balanceChange,
      last_updated: new Date()
    }, { transaction });
    
    await transaction.commit();
    
    // İlişkili verileri yükle
    return await Transaction.findByPk(newTransaction.id, {
      include: [
        { model: Account },
        { model: User, as: 'creator' },
        { model: Reservation, as: 'Reservation' }
      ]
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * İşlem güncelleme servisi
 * @param {number} id - İşlem ID
 * @param {Object} transactionData - Güncellenecek veriler
 * @returns {Object} Güncellenen işlem
 */
exports.updateTransaction = async (id, transactionData) => {
  const { description, referenceNumber } = transactionData;

  // İşlemi bul
  const transaction = await Transaction.findByPk(id);
  
  if (!transaction) {
    throw new AppError('İşlem bulunamadı', 404);
  }
  
  // Sadece açıklama ve referans numarası güncellenebilir
  await transaction.update({
    description: description !== undefined ? description : transaction.description,
    reference_number: referenceNumber !== undefined ? referenceNumber : transaction.reference_number
  });
  
  return transaction;
};

/**
 * İşlem silme servisi
 * @param {number} id - İşlem ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteTransaction = async (id) => {
  // İşlemi bul
  const transaction = await Transaction.findByPk(id);
  
  if (!transaction) {
    throw new AppError('İşlem bulunamadı', 404);
  }
  
  // Hesabı bul
  const account = await Account.findByPk(transaction.account_id);
  
  if (!account) {
    throw new AppError('Hesap bulunamadı', 404);
  }
  
  // İşlem başlat
  const dbTransaction = await sequelize.transaction();
  
  try {
    // Hesap bakiyesini güncelle
    let balanceChange = 0;
    
    if (transaction.transaction_type === 'payment' || transaction.transaction_type === 'refund') {
      // Ödeme veya iade: bakiyeyi azalt
      balanceChange = -parseFloat(transaction.amount);
    } else if (transaction.transaction_type === 'charge' || transaction.transaction_type === 'adjustment') {
      // Tahsilat veya düzeltme: bakiyeyi artır
      balanceChange = parseFloat(transaction.amount);
    }
    
    await account.update({
      balance: parseFloat(account.balance) + balanceChange,
      last_updated: new Date()
    }, { transaction: dbTransaction });
    
    // İşlemi sil
    await transaction.destroy({ transaction: dbTransaction });
    
    await dbTransaction.commit();
    return true;
  } catch (error) {
    await dbTransaction.rollback();
    throw error;
  }
};

/**
 * Rezervasyona ait işlemleri getirme servisi
 * @param {number} reservationId - Rezervasyon ID
 * @returns {Array} İşlem listesi
 */
exports.getTransactionsByReservation = async (reservationId) => {
  // Rezervasyon kontrolü
  const reservation = await Reservation.findByPk(reservationId);
  if (!reservation) {
    throw new AppError('Rezervasyon bulunamadı', 404);
  }
  
  // İşlemleri getir
  const transactions = await Transaction.findAll({
    where: { reservation_id: reservationId },
    include: [
      { model: Account },
      { model: User, as: 'creator' }
    ],
    order: [['transaction_date', 'DESC']]
  });
  
  return transactions;
};

/**
 * Ödeme işlemi oluşturma servisi
 * @param {Object} paymentData - Ödeme verileri
 * @returns {Object} Oluşturulan ödeme işlemi
 */
exports.createPayment = async (paymentData) => {
  const { 
    accountId, 
    amount, 
    currency, 
    description, 
    referenceNumber, 
    reservationId,
    createdBy
  } = paymentData;
  
  return await this.createTransaction({
    accountId,
    amount,
    currency,
    transactionType: 'payment',
    description: description || 'Ödeme',
    referenceNumber,
    reservationId,
    createdBy
  });
};

/**
 * İade işlemi oluşturma servisi
 * @param {Object} refundData - İade verileri
 * @returns {Object} Oluşturulan iade işlemi
 */
exports.createRefund = async (refundData) => {
  const { 
    accountId, 
    amount, 
    currency, 
    description, 
    referenceNumber, 
    reservationId,
    createdBy
  } = refundData;
  
  return await this.createTransaction({
    accountId,
    amount,
    currency,
    transactionType: 'refund',
    description: description || 'İade',
    referenceNumber,
    reservationId,
    createdBy
  });
};
