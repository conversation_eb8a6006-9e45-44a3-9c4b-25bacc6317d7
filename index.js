const app = require("./src/app");
const { createServer } = require("http");
const { sequelize } = require('./src/models');

const PORT = process.env.PORT || 3000;

// Veritabanı bağlantısını test et
const testDatabaseConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('Veritabanı bağlantısı başarılı.');

    // Geliştirme ortamında sync yapılabilir
    if (process.env.NODE_ENV === 'development' && process.env.DB_SYNC === 'true') {
      console.log('Veritabanı modelleri senkronize ediliyor...');
      await sequelize.sync({ alter: true });
      console.log('Veritabanı modelleri senkronize edildi.');
    }

    return true;
  } catch (error) {
    console.error('Veritabanı bağlantı hatası:', error);
    return false;
  }
};

// HTTP sunucusu oluştur
const httpServer = createServer(app);

// Sunucuyu başlat
const startServer = async () => {
  // Önce veritabanı bağlantısını kontrol et
  const dbConnected = await testDatabaseConnection();

  if (dbConnected) {
    httpServer.listen(PORT, () => {
      console.log(`Server ${PORT} portunda çalışıyor. (${process.env.NODE_ENV} modu)`);
    });
  } else {
    console.error('Veritabanı bağlantısı başarısız olduğu için sunucu başlatılamadı.');
    process.exit(1);
  }
};

// Sunucuyu başlat
startServer();

// Beklenmeyen hataları yakala
process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION! 💥 Sunucu kapatılıyor...');
  console.error(err.name, err.message);
  httpServer.close(() => {
    process.exit(1);
  });
});

process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION! 💥 Sunucu kapatılıyor...');
  console.error(err.name, err.message);
  process.exit(1);
});