const { Umzug, SequelizeStorage } = require('umzug');
const { sequelize } = require('./src/models');
const path = require('path');

// Migration'ları çalıştır
async function runMigrations() {
  try {
    // Migrations tablosunu oluştur (e<PERSON><PERSON> yoksa)
    await sequelize.query('CREATE TABLE IF NOT EXISTS `SequelizeMeta` (`name` VARCHAR(255) NOT NULL PRIMARY KEY)');

    const umzug = new Umzug({
      migrations: {
        glob: 'src/migrations/*.js',
        resolve: ({ name, path, context }) => {
          const migration = require(path);
          return {
            name,
            up: async () => migration.up(context.queryInterface, context.sequelize),
            down: async () => migration.down(context.queryInterface, context.sequelize),
          };
        },
      },
      context: {
        queryInterface: sequelize.getQueryInterface(),
        sequelize: sequelize.constructor
      },
      storage: new SequelizeStorage({ sequelize }),
      logger: console,
    });

    // Bekleyen migration'ları çalıştır
    const migrations = await umzug.up();
    console.log('Migrations completed:', migrations.map(m => m.name));

    console.log('Database migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

runMigrations();
