// server/controllers/account.controller.js

const accountService = require('../services/account.service');

/**
 * Tüm hesapları getir
 * @route GET /api/accounts
 */
exports.getAllAccounts = async (req, res, next) => {
  try {
    const { 
      entityType, 
      entityId, 
      currency,
      page = 1, 
      limit = 20,
      sortBy = 'id',
      sortDir = 'ASC'
    } = req.query;

    const filters = {
      entityType,
      entityId,
      currency,
      page,
      limit,
      sortBy,
      sortDir
    };

    const result = await accountService.getAllAccounts(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Hesap detaylarını getir
 * @route GET /api/accounts/:id
 */
exports.getAccountById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const account = await accountService.getAccountById(id);
    
    res.status(200).json({
      success: true,
      data: account
    });
  } catch (error) {
    next(error);
  }
};

/**
 * <PERSON><PERSON><PERSON> bir varlı<PERSON> (acente/taşımacı) ait hesabı getir
 * @route GET /api/accounts/entity/:type/:id
 */
exports.getAccountByEntity = async (req, res, next) => {
  try {
    const { type, id } = req.params;
    const { currency } = req.query;
    
    const account = await accountService.getAccountByEntity(type, id, currency);
    
    res.status(200).json({
      success: true,
      data: account
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni hesap oluştur
 * @route POST /api/accounts
 */
exports.createAccount = async (req, res, next) => {
  try {
    const { entityType, entityId, balance, currency } = req.body;
    
    const newAccount = await accountService.createAccount({
      entityType,
      entityId,
      balance,
      currency
    });
    
    res.status(201).json({
      success: true,
      data: newAccount,
      message: 'Hesap başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Hesap güncelle
 * @route PUT /api/accounts/:id
 */
exports.updateAccount = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { balance, currency } = req.body;
    
    const updatedAccount = await accountService.updateAccount(id, {
      balance,
      currency
    });
    
    res.status(200).json({
      success: true,
      data: updatedAccount,
      message: 'Hesap başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Hesap işlemlerini getir
 * @route GET /api/accounts/:id/transactions
 */
exports.getAccountTransactions = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      startDate, 
      endDate, 
      transactionType,
      page = 1, 
      limit = 20,
      sortBy = 'transaction_date',
      sortDir = 'DESC'
    } = req.query;
    
    const filters = {
      accountId: id,
      startDate,
      endDate,
      transactionType,
      page,
      limit,
      sortBy,
      sortDir
    };
    
    const result = await accountService.getAccountTransactions(filters);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Hesap ekstresini getir
 * @route GET /api/accounts/:id/statement
 */
exports.generateAccountStatement = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, format = 'pdf' } = req.query;
    
    const statement = await accountService.generateAccountStatement(id, startDate, endDate, format);
    
    if (format === 'json') {
      return res.status(200).json({
        success: true,
        data: statement
      });
    }
    
    // PDF formatı için dosya indirme
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=account-statement-${id}.pdf`);
    res.send(statement);
    
  } catch (error) {
    next(error);
  }
};
