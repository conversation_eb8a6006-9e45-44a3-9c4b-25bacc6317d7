// server/services/price.service.js

const {
  Price,
  Agency,
  Carrier,
  Region,
  VehicleType,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm fiyatları listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Fiyat listesi ve sayfalama bilgileri
 */
exports.getAllPrices = async (filters) => {
  const {
    agencyId,
    carrierId,
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    page = 1,
    limit = 20,
    sortBy = 'id',
    sortDir = 'ASC'
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {};

  if (agencyId) {
    whereConditions.agency_id = agencyId;
  }

  if (carrierId) {
    whereConditions.carrier_id = carrierId;
  }

  if (fromRegionId) {
    whereConditions.from_region_id = fromRegionId;
  }

  if (toRegionId) {
    whereConditions.to_region_id = toRegionId;
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Price.findAndCountAll({
    where: whereConditions,
    include: [
      { model: Agency, as: 'Agency' },
      { model: Carrier, as: 'Carrier' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' }
    ],
    order,
    limit: limitNum,  // Burada limitNum kullanılmalı
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    prices: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Belirli bir acenteye ait fiyatları getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Fiyat listesi ve sayfalama bilgileri
 */
exports.getPricesByAgency = async (filters) => {
  const {
    agencyId,
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    page = 1,
    limit = 20
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {
    agency_id: agencyId
  };

  if (fromRegionId) {
    whereConditions.from_region_id = fromRegionId;
  }

  if (toRegionId) {
    whereConditions.to_region_id = toRegionId;
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Veritabanı sorgusu
  const { count, rows } = await Price.findAndCountAll({
    where: whereConditions,
    include: [
      { model: Agency, as: 'Agency' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' }
    ],
    order: [['valid_from', 'DESC']],
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Belirli bir fiyatı ID'ye göre getirme servisi
 * @param {number} id - Fiyat ID
 * @returns {Object} Fiyat bilgisi
 */
exports.getPriceById = async (id) => {
  const price = await Price.findByPk(id, {
    include: [
      { model: Agency, as: 'Agency' },
      { model: Carrier, as: 'Carrier' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' }
    ]
  });

  if (!price) {
    throw new AppError('Fiyat bulunamadı', 404);
  }

  return price;
};

/**
 * Belirli bir taşıyıcıya ait fiyatları getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Fiyat listesi ve sayfalama bilgileri
 */
exports.getPricesByCarrier = async (filters) => {
  const {
    carrierId,
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    page = 1,
    limit = 20
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {
    carrier_id: carrierId
  };

  if (fromRegionId) {
    whereConditions.from_region_id = fromRegionId;
  }

  if (toRegionId) {
    whereConditions.to_region_id = toRegionId;
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Veritabanı sorgusu
  const { count, rows } = await Price.findAndCountAll({
    where: whereConditions,
    include: [
      { model: Carrier, as: 'Carrier' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' }
    ],
    order: [['valid_from', 'DESC']],
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * İki bölge arasındaki fiyatı hesaplama servisi
 * @param {number} agencyId - Acente ID
 * @param {number} fromRegionId - Başlangıç bölgesi ID
 * @param {number} toRegionId - Varış bölgesi ID
 * @param {number} vehicleTypeId - Araç tipi ID
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Object} Fiyat bilgisi
 */
exports.calculatePrice = async (agencyId, carrierId, fromRegionId, toRegionId, vehicleTypeId, date) => {
  // Tarih kontrolü
  const targetDate = date ? new Date(date) : new Date();

  // Fiyat sorgusu
  const whereConditions = {
    from_region_id: fromRegionId,
    to_region_id: toRegionId,
    vehicle_type_id: vehicleTypeId,
  };

  if (agencyId) {
    whereConditions.agency_id = agencyId;
  }

  if (carrierId) {
    whereConditions.carrier_id = carrierId;
  }

  // En az bir tane acente veya taşıyıcı olmalı
  if (!agencyId && !carrierId) {
    throw new AppError('Acente veya taşıyıcı belirtilmelidir', 400);
  }

  // Tarih koşullarını ekle
  whereConditions[Op.and] = [
    {
      [Op.or]: [
        { valid_from: { [Op.lte]: targetDate } },
        { valid_from: null }
      ]
    },
    {
      [Op.or]: [
        { valid_to: { [Op.gte]: targetDate } },
        { valid_to: null }
      ]
    }
  ];

  // Fiyat sorgusu
  const price = await Price.findOne({
    where: whereConditions,
    order: [['valid_from', 'DESC']],
    include: [
      { model: Agency, as: 'Agency' },
      { model: Carrier, as: 'Carrier' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' }
    ]
  });

  if (!price) {
    throw new AppError('Bu rota için fiyat bulunamadı', 404);
  }

  return price;
};

/**
 * Yeni fiyat oluşturma servisi
 * @param {Object} priceData - Fiyat verileri
 * @returns {Object} Oluşturulan fiyat
 */
exports.createPrice = async (priceData) => {
  const {
    agencyId,
    carrierId,
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    price,
    currency = 'EUR',
    validFrom,
    validTo
  } = priceData;

  // Varlık kontrolü
  if (agencyId) {
    const agency = await Agency.findByPk(agencyId);
    if (!agency) {
      throw new AppError('Acente bulunamadı', 404);
    }
  }

  if (carrierId) {
    const carrier = await Carrier.findByPk(carrierId);
    if (!carrier) {
      throw new AppError('Taşıyıcı bulunamadı', 404);
    }
  }

  // En az bir tane acente veya taşıyıcı olmalı
  if (!agencyId && !carrierId) {
    throw new AppError('Acente veya taşıyıcı belirtilmelidir', 400);
  }

  const fromRegion = await Region.findByPk(fromRegionId);
  if (!fromRegion) {
    throw new AppError('Başlangıç bölgesi bulunamadı', 404);
  }

  const toRegion = await Region.findByPk(toRegionId);
  if (!toRegion) {
    throw new AppError('Varış bölgesi bulunamadı', 404);
  }

  const vehicleType = await VehicleType.findByPk(vehicleTypeId);
  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }

  // Aynı rota için aynı tarih aralığında fiyat var mı kontrol et
  if (validFrom) {
    const whereConditions = {
      from_region_id: fromRegionId,
      to_region_id: toRegionId,
      vehicle_type_id: vehicleTypeId,
      valid_from: validFrom
    };

    if (agencyId) {
      whereConditions.agency_id = agencyId;
    }

    if (carrierId) {
      whereConditions.carrier_id = carrierId;
    }

    const existingPrice = await Price.findOne({
      where: whereConditions
    });

    if (existingPrice) {
      throw new AppError('Bu rota için aynı başlangıç tarihli fiyat zaten mevcut', 400);
    }
  }

  // Yeni fiyat oluştur
  const priceObj = {
    from_region_id: fromRegionId,
    to_region_id: toRegionId,
    vehicle_type_id: vehicleTypeId,
    price,
    currency,
    valid_from: validFrom,
    valid_to: validTo
  };

  if (agencyId) {
    priceObj.agency_id = agencyId;
  }

  if (carrierId) {
    priceObj.carrier_id = carrierId;
  }

  const newPrice = await Price.create(priceObj);

  return newPrice;
};

/**
 * Fiyat güncelleme servisi
 * @param {number} id - Fiyat ID
 * @param {Object} priceData - Güncellenecek veriler
 * @returns {Object} Güncellenen fiyat
 */
exports.updatePrice = async (id, priceData) => {
  const {
    agencyId,
    carrierId,
    fromRegionId,
    toRegionId,
    vehicleTypeId,
    price,
    currency,
    validFrom,
    validTo
  } = priceData;

  // Fiyatı bul
  const priceRecord = await Price.findByPk(id);

  if (!priceRecord) {
    throw new AppError('Fiyat bulunamadı', 404);
  }

  // Varlık kontrolü (eğer değiştiyse)
  if (agencyId && agencyId !== priceRecord.agency_id) {
    const agency = await Agency.findByPk(agencyId);
    if (!agency) {
      throw new AppError('Acente bulunamadı', 404);
    }
  }

  if (carrierId && carrierId !== priceRecord.carrier_id) {
    const carrier = await Carrier.findByPk(carrierId);
    if (!carrier) {
      throw new AppError('Taşıyıcı bulunamadı', 404);
    }
  }

  if (fromRegionId && fromRegionId !== priceRecord.from_region_id) {
    const fromRegion = await Region.findByPk(fromRegionId);
    if (!fromRegion) {
      throw new AppError('Başlangıç bölgesi bulunamadı', 404);
    }
  }

  if (toRegionId && toRegionId !== priceRecord.to_region_id) {
    const toRegion = await Region.findByPk(toRegionId);
    if (!toRegion) {
      throw new AppError('Varış bölgesi bulunamadı', 404);
    }
  }

  if (vehicleTypeId && vehicleTypeId !== priceRecord.vehicle_type_id) {
    const vehicleType = await VehicleType.findByPk(vehicleTypeId);
    if (!vehicleType) {
      throw new AppError('Araç tipi bulunamadı', 404);
    }
  }

  // Aynı rota için aynı tarih aralığında fiyat var mı kontrol et (eğer değiştiyse)
  if (validFrom && validFrom !== priceRecord.valid_from) {
    const whereConditions = {
      from_region_id: fromRegionId || priceRecord.from_region_id,
      to_region_id: toRegionId || priceRecord.to_region_id,
      vehicle_type_id: vehicleTypeId || priceRecord.vehicle_type_id,
      valid_from: validFrom,
      id: { [Op.ne]: id }
    };

    if (agencyId || priceRecord.agency_id) {
      whereConditions.agency_id = agencyId || priceRecord.agency_id;
    }

    if (carrierId || priceRecord.carrier_id) {
      whereConditions.carrier_id = carrierId || priceRecord.carrier_id;
    }

    const existingPrice = await Price.findOne({
      where: whereConditions
    });

    if (existingPrice) {
      throw new AppError('Bu rota için aynı başlangıç tarihli fiyat zaten mevcut', 400);
    }
  }

  // Güncelle
  const updateData = {
    from_region_id: fromRegionId || priceRecord.from_region_id,
    to_region_id: toRegionId || priceRecord.to_region_id,
    vehicle_type_id: vehicleTypeId || priceRecord.vehicle_type_id,
  };

  if (agencyId !== undefined) {
    updateData.agency_id = agencyId;
  }

  if (carrierId !== undefined) {
    updateData.carrier_id = carrierId;
  }

  await priceRecord.update({
    ...updateData,
    price: price !== undefined ? price : priceRecord.price,
    currency: currency || priceRecord.currency,
    valid_from: validFrom !== undefined ? validFrom : priceRecord.valid_from,
    valid_to: validTo !== undefined ? validTo : priceRecord.valid_to
  });

  return priceRecord;
};

/**
 * Fiyat silme servisi
 * @param {number} id - Fiyat ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deletePrice = async (id) => {
  // Fiyatı bul
  const price = await Price.findByPk(id);

  if (!price) {
    throw new AppError('Fiyat bulunamadı', 404);
  }

  // Sil
  await price.destroy();

  return true;
};

/**
 * Toplu fiyat oluşturma/güncelleme servisi
 * @param {Array} prices - Fiyat verileri dizisi
 * @returns {Object} İşlem sonucu
 */
exports.bulkUpdatePrices = async (prices) => {
  const transaction = await sequelize.transaction();

  try {
    const results = {
      created: 0,
      updated: 0,
      failed: 0,
      errors: []
    };

    for (const priceData of prices) {
      try {
        const {
          id,
          agencyId,
          carrierId,
          fromRegionId,
          toRegionId,
          vehicleTypeId,
          price,
          currency,
          validFrom,
          validTo
        } = priceData;

        // Güncelleme veya oluşturma
        if (id) {
          // Mevcut fiyatı güncelle
          const priceRecord = await Price.findByPk(id, { transaction });

          if (!priceRecord) {
            throw new AppError(`ID: ${id} - Fiyat bulunamadı`, 404);
          }

          const updateData = {
            from_region_id: fromRegionId || priceRecord.from_region_id,
            to_region_id: toRegionId || priceRecord.to_region_id,
            vehicle_type_id: vehicleTypeId || priceRecord.vehicle_type_id,
            price: price !== undefined ? price : priceRecord.price,
            currency: currency || priceRecord.currency,
            valid_from: validFrom !== undefined ? validFrom : priceRecord.valid_from,
            valid_to: validTo !== undefined ? validTo : priceRecord.valid_to
          };

          if (agencyId !== undefined) {
            updateData.agency_id = agencyId;
          }

          if (carrierId !== undefined) {
            updateData.carrier_id = carrierId;
          }

          await priceRecord.update(updateData, { transaction });

          results.updated++;
        } else {
          // Yeni fiyat oluştur
          const createData = {
            from_region_id: fromRegionId,
            to_region_id: toRegionId,
            vehicle_type_id: vehicleTypeId,
            price,
            currency: currency || 'EUR',
            valid_from: validFrom,
            valid_to: validTo
          };

          if (agencyId) {
            createData.agency_id = agencyId;
          }

          if (carrierId) {
            createData.carrier_id = carrierId;
          }

          // En az bir tane acente veya taşıyıcı olmalı
          if (!agencyId && !carrierId) {
            throw new AppError('Acente veya taşıyıcı belirtilmelidir', 400);
          }

          await Price.create(createData, { transaction });

          results.created++;
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          data: priceData,
          message: error.message
        });
      }
    }

    await transaction.commit();
    return results;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
