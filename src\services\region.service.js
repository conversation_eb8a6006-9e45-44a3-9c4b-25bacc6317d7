// server/services/region.service.js

const {
  Region,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm bölgeleri listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Bölge listesi ve sayfalama bilgileri
 */
exports.getAllRegions = async (filters) => {
  const {
    name,
    category,
    parentRegionId,
    page = 1,
    limit = 20,
    sortBy = 'name',
    sortDir = 'ASC'
  } = filters;

  // Sayısal değerleri kontrol et
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {};

  if (name) {
    whereConditions.name = { [Op.like]: `%${name}%` };
  }

  if (category) {
    whereConditions.category = category;
  }

  if (parentRegionId) {
    whereConditions.parent_region_id = parentRegionId;
  } else if (parentRegionId === null) {
    whereConditions.parent_region_id = null;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Region.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: Region,
        as: 'parentRegion',
        attributes: ['id', 'name', 'category']
      }
    ],
    order,
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    regions: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Bölge detaylarını getirme servisi
 * @param {number} id - Bölge ID
 * @returns {Object} Bölge detayları
 */
exports.getRegionById = async (id) => {
  const region = await Region.findByPk(id, {
    include: [
      {
        model: Region,
        as: 'parentRegion',
        attributes: ['id', 'name', 'category']
      },
      {
        model: Region,
        as: 'subRegions',
        attributes: ['id', 'name', 'category']
      }
    ]
  });

  if (!region) {
    throw new AppError('Bölge bulunamadı', 404);
  }

  // Sonuç
  return {
    ...region.toJSON(),
    hotelCount: 0 // Artık hotels tablosu olmadığı için 0 dönüyöruz
  };
};

/**
 * Yeni bölge oluşturma servisi
 * @param {Object} regionData - Bölge verileri
 * @returns {Object} Oluşturulan bölge
 */
exports.createRegion = async (regionData) => {
  const {
    name,
    category,
    parentRegionId
  } = regionData;

  // Kategori kontrolü
  const validCategories = ['airport', 'district', 'hotel', 'other'];
  if (!validCategories.includes(category)) {
    throw new AppError('Geçersiz kategori', 400);
  }

  // Üst bölge kontrolü (eğer belirtildiyse)
  if (parentRegionId) {
    const parentRegion = await Region.findByPk(parentRegionId);
    if (!parentRegion) {
      throw new AppError('Üst bölge bulunamadı', 404);
    }
  }

  // Boş string kontrolü ve NULL dönüştürme
  const parentId = parentRegionId === '' ? null : parentRegionId;

  // Yeni bölge oluştur
  const newRegion = await Region.create({
    name,
    category,
    parent_region_id: parentId
  });

  return newRegion;
};

/**
 * Bölge güncelleme servisi
 * @param {number} id - Bölge ID
 * @param {Object} regionData - Güncellenecek veriler
 * @returns {Object} Güncellenen bölge
 */
exports.updateRegion = async (id, regionData) => {
  const {
    name,
    category,
    parentRegionId
  } = regionData;

  // Bölgeyi bul
  const region = await Region.findByPk(id);

  if (!region) {
    throw new AppError('Bölge bulunamadı', 404);
  }

  // Kategori kontrolü (eğer değiştiyse)
  if (category) {
    const validCategories = ['airport', 'district', 'hotel', 'other'];
    if (!validCategories.includes(category)) {
      throw new AppError('Geçersiz kategori', 400);
    }
  }

  // Üst bölge kontrolü (eğer değiştiyse)
  if (parentRegionId && parentRegionId !== region.parent_region_id) {
    // Kendisini üst bölge olarak seçmeye çalışıyorsa engelle
    if (parentRegionId == id) {
      throw new AppError('Bir bölge kendisini üst bölge olarak seçemez', 400);
    }

    const parentRegion = await Region.findByPk(parentRegionId);
    if (!parentRegion) {
      throw new AppError('Üst bölge bulunamadı', 404);
    }

    // Döngüsel bağımlılık kontrolü
    let currentParent = parentRegion;
    while (currentParent && currentParent.parent_region_id) {
      if (currentParent.parent_region_id == id) {
        throw new AppError('Döngüsel bağımlılık oluşturulamaz', 400);
      }
      currentParent = await Region.findByPk(currentParent.parent_region_id);
    }
  }

  // Boş string kontrolü ve NULL dönüştürme
  let parentId = region.parent_region_id;
  if (parentRegionId !== undefined) {
    parentId = parentRegionId === '' ? null : parentRegionId;
  }

  // Güncelle
  await region.update({
    name: name || region.name,
    category: category || region.category,
    parent_region_id: parentId
  });

  return region;
};

/**
 * Bölge silme servisi
 * @param {number} id - Bölge ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteRegion = async (id) => {
  // Bölgeyi bul
  const region = await Region.findByPk(id);

  if (!region) {
    throw new AppError('Bölge bulunamadı', 404);
  }

  // Alt bölgeler var mı kontrol et
  const childRegionCount = await Region.count({
    where: { parent_region_id: id }
  });

  if (childRegionCount > 0) {
    throw new AppError('Bu bölgeye bağlı alt bölgeler bulunduğu için silinemez', 400);
  }

  // Bölgeye ait oteller var mı kontrol et
  const hotelCount = await Hotel.count({
    where: { region_id: id }
  });

  if (hotelCount > 0) {
    throw new AppError('Bu bölgeye ait oteller bulunduğu için silinemez', 400);
  }

  // Sil
  await region.destroy();

  return true;
};

/**
 * Kategoriye göre bölgeleri getirme servisi
 * @param {string} category - Bölge kategorisi
 * @returns {Array} Bölge listesi
 */
exports.getRegionsByCategory = async (category) => {
  // Kategori kontrolü
  const validCategories = ['airport', 'district', 'hotel', 'other'];
  if (!validCategories.includes(category)) {
    throw new AppError('Geçersiz kategori', 400);
  }

  // Bölgeleri getir
  const regions = await Region.findAll({
    where: { category },
    include: [
      {
        model: Region,
        as: 'parentRegion',
        attributes: ['id', 'name', 'category']
      }
    ],
    order: [['name', 'ASC']]
  });

  return regions;
};

/**
 * Bölgeye ait otelleri getirme servisi - Artık kullanılmıyor
 * @param {number} regionId - Bölge ID
 * @returns {Array} Boş dizi
 */
exports.getRegionHotels = async (regionId) => {
  // Bölgeyi bul
  const region = await Region.findByPk(regionId);

  if (!region) {
    throw new AppError('Bölge bulunamadı', 404);
  }

  // Artık hotels tablosu olmadığı için boş dizi dönüyöruz
  return [];
};

/**
 * Bölgeye otel ekleme servisi - Artık kullanılmıyor
 * @param {number} regionId - Bölge ID
 * @param {Object} hotelData - Otel verileri
 * @returns {Object} Hata nesnesi
 */
exports.addHotelToRegion = async (regionId, hotelData) => {
  // Bölgeyi bul
  const region = await Region.findByPk(regionId);

  if (!region) {
    throw new AppError('Bölge bulunamadı', 404);
  }

  // Artık hotels tablosu olmadığı için hata dönüyöruz
  throw new AppError('Otel ekleme özelliği artık desteklenmiyor. Lütfen rezervasyon yaparken otel adını doğrudan girin.', 400);
};
