// server/middleware/upload.js

const multer = require('multer');
const path = require('path');
const { getSafeFilename, isValidFileExtension } = require('../utils/helpers');
const AppError = require('../utils/appError');

// Do<PERSON>a yükleme dizini
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// Dosya depolama yapılandırması
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Dosya tipine göre alt klasör belirle
    let uploadPath = UPLOAD_DIR;
    
    if (req.originalUrl.includes('/vehicles')) {
      uploadPath = path.join(UPLOAD_DIR, 'vehicles');
    } else if (req.originalUrl.includes('/documents')) {
      uploadPath = path.join(UPLOAD_DIR, 'documents');
    } else if (req.originalUrl.includes('/users')) {
      uploadPath = path.join(UPLOAD_DIR, 'users');
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Güvenli dosya adı oluştur
    const safeFileName = getSafeFilename(file.originalname);
    cb(null, safeFileName);
  }
});

// Dosya filtresi
const fileFilter = (req, file, cb) => {
  // Dosya uzantısını kontrol et
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx'];
  
  if (req.originalUrl.includes('/vehicles/photos') || req.originalUrl.includes('/driver-photo')) {
    // Sadece resim dosyaları
    if (isValidFileExtension(file.originalname, ['.jpg', '.jpeg', '.png'])) {
      cb(null, true);
    } else {
      cb(new AppError('Sadece JPG, JPEG ve PNG dosyaları kabul edilir', 400), false);
    }
  } else if (req.originalUrl.includes('/documents')) {
    // Tüm izin verilen dosya tipleri
    if (isValidFileExtension(file.originalname, allowedExtensions)) {
      cb(null, true);
    } else {
      cb(new AppError('Geçersiz dosya formatı', 400), false);
    }
  } else {
    // Varsayılan olarak tüm dosya tiplerini kabul et
    cb(null, true);
  }
};

// Multer yapılandırması
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

module.exports = upload;
