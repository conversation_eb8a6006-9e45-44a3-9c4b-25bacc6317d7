// server/routes/transaction.routes.js

const express = require('express');
const router = express.Router();
const transactionController = require('../controllers/transaction.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');

/**
 * @route GET /api/transactions
 * @desc Tüm işlemleri getir
 * @access Private
 */
router.get(
  '/', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  transactionController.getAllTransactions
);

/**
 * @route GET /api/transactions/:id
 * @desc İşlem detaylarını getir
 * @access Private
 */
router.get(
  '/:id', 
  authenticate, 
  authorize(['admin', 'manager', 'finance']),
  transactionController.getTransactionById
);

/**
 * @route POST /api/transactions
 * @desc Yeni işlem oluştur
 * @access Private
 */
router.post(
  '/', 
  authenticate, 
  checkPermission('can_manage_payments'),
  transactionController.createTransaction
);

/**
 * @route PUT /api/transactions/:id
 * @desc İşlem güncelle
 * @access Private
 */
router.put(
  '/:id', 
  authenticate, 
  authorize(['admin', 'finance']),
  transactionController.updateTransaction
);

/**
 * @route DELETE /api/transactions/:id
 * @desc İşlem sil
 * @access Private
 */
router.delete(
  '/:id', 
  authenticate, 
  authorize(['admin']),
  transactionController.deleteTransaction
);

/**
 * @route GET /api/transactions/reservation/:reservationId
 * @desc Rezervasyona ait işlemleri getir
 * @access Private
 */
router.get(
  '/reservation/:reservationId', 
  authenticate, 
  transactionController.getTransactionsByReservation
);

/**
 * @route POST /api/transactions/payment
 * @desc Ödeme işlemi oluştur
 * @access Private
 */
router.post(
  '/payment', 
  authenticate, 
  checkPermission('can_manage_payments'),
  transactionController.createPayment
);

/**
 * @route POST /api/transactions/refund
 * @desc İade işlemi oluştur
 * @access Private
 */
router.post(
  '/refund', 
  authenticate, 
  authorize(['admin', 'finance']),
  transactionController.createRefund
);

module.exports = router;
