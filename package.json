{"name": "operasyon-sistemi-server", "version": "1.0.0", "description": "Transfer ve operasyon yö<PERSON> - Backend", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js"}, "keywords": ["nodejs", "express", "mysql", "sequelize", "api"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "nodemailer": "^6.9.4", "pdfkit": "^0.13.0", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1", "umzug": "^3.8.2", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}