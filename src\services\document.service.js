// server/services/document.service.js

const { 
  Document, 
  User,
  sequelize 
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Dosya yükleme dizini
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// Dizin yoksa oluştur
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Tüm belgeleri listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Belge listesi ve sayfalama bilgileri
 */
exports.getAllDocuments = async (filters) => {
  const { 
    entityType, 
    entityId, 
    documentType,
    page = 1, 
    limit = 20,
    sortBy = 'upload_date',
    sortDir = 'DESC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {};
  
  if (entityType) {
    whereConditions.entity_type = entityType;
  }
  
  if (entityId) {
    whereConditions.entity_id = entityId;
  }
  
  if (documentType) {
    whereConditions.document_type = documentType;
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Document.findAndCountAll({
    where: whereConditions,
    include: [
      { model: User, as: 'uploader' }
    ],
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    documents: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Belge detaylarını getirme servisi
 * @param {number} id - Belge ID
 * @returns {Object} Belge detayları
 */
exports.getDocumentById = async (id) => {
  const document = await Document.findByPk(id, {
    include: [
      { model: User, as: 'uploader' }
    ]
  });
  
  if (!document) {
    throw new AppError('Belge bulunamadı', 404);
  }
  
  return document;
};

/**
 * Belge yükleme servisi
 * @param {Object} documentData - Belge verileri
 * @returns {Object} Yüklenen belge
 */
exports.uploadDocument = async (documentData) => {
  const { entityType, entityId, documentType, file, uploadedBy } = documentData;

  if (!file) {
    throw new AppError('Dosya bulunamadı', 400);
  }

  // Dosya adını benzersiz yap
  const fileExtension = path.extname(file.originalname);
  const uniqueFileName = `${uuidv4()}${fileExtension}`;
  
  // Dosya yolu
  const filePath = path.join(UPLOAD_DIR, uniqueFileName);
  
  // Dosyayı kaydet
  try {
    fs.writeFileSync(filePath, file.buffer);
  } catch (error) {
    throw new AppError('Dosya kaydedilemedi', 500);
  }
  
  // Veritabanına kaydet
  const newDocument = await Document.create({
    entity_type: entityType,
    entity_id: entityId,
    document_type: documentType,
    file_name: file.originalname,
    file_path: `uploads/${uniqueFileName}`,
    upload_date: new Date(),
    uploaded_by: uploadedBy
  });
  
  return newDocument;
};

/**
 * Belge silme servisi
 * @param {number} id - Belge ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteDocument = async (id) => {
  // Belgeyi bul
  const document = await Document.findByPk(id);
  
  if (!document) {
    throw new AppError('Belge bulunamadı', 404);
  }
  
  // Dosyayı sil
  const filePath = path.join(__dirname, '../../', document.file_path);
  
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Dosya silinirken hata oluştu:', error);
    // Dosya silinmese bile veritabanından kaydı silmeye devam et
  }
  
  // Veritabanından sil
  await document.destroy();
  
  return true;
};

/**
 * Belirli bir varlığa ait belgeleri getirme servisi
 * @param {string} type - Varlık tipi
 * @param {number} id - Varlık ID
 * @param {string} documentType - Belge tipi (opsiyonel)
 * @returns {Array} Belge listesi
 */
exports.getDocumentsByEntity = async (type, id, documentType) => {
  // Filtreleme koşulları
  const whereConditions = {
    entity_type: type,
    entity_id: id
  };
  
  if (documentType) {
    whereConditions.document_type = documentType;
  }
  
  // Belgeleri getir
  const documents = await Document.findAll({
    where: whereConditions,
    include: [
      { model: User, as: 'uploader' }
    ],
    order: [['upload_date', 'DESC']]
  });
  
  return documents;
};

/**
 * İndirme için belge getirme servisi
 * @param {number} id - Belge ID
 * @returns {Object} Belge bilgileri
 */
exports.getDocumentForDownload = async (id) => {
  // Belgeyi bul
  const document = await Document.findByPk(id);
  
  if (!document) {
    throw new AppError('Belge bulunamadı', 404);
  }
  
  // Dosya yolu
  const filePath = path.join(__dirname, '../../', document.file_path);
  
  // Dosya var mı kontrol et
  if (!fs.existsSync(filePath)) {
    throw new AppError('Dosya bulunamadı', 404);
  }
  
  return {
    fileName: document.file_name,
    filePath
  };
};
