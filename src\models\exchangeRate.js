module.exports = (sequelize, DataTypes) => {
  const ExchangeRate = sequelize.define('ExchangeRate', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    from_currency: {
      type: DataTypes.STRING(3),
      allowNull: false
    },
    to_currency: {
      type: DataTypes.STRING(3),
      allowNull: false
    },
    rate: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: false
    },
    effective_date: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    tableName: 'exchange_rates',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['from_currency', 'to_currency', 'effective_date']
      }
    ]
  });

  return ExchangeRate;
};
