// server/services/exchangeRate.service.js

const { 
  ExchangeRate, 
  sequelize 
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');
const axios = require('axios');

/**
 * Tüm döviz kurlarını listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Döviz kuru listesi ve sayfalama bilgileri
 */
exports.getAllExchangeRates = async (filters) => {
  const { 
    fromCurrency, 
    toCurrency, 
    startDate, 
    endDate,
    page = 1, 
    limit = 20,
    sortBy = 'effective_date',
    sortDir = 'DESC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {};
  
  if (fromCurrency) {
    whereConditions.from_currency = fromCurrency;
  }
  
  if (toCurrency) {
    whereConditions.to_currency = toCurrency;
  }
  
  if (startDate && endDate) {
    whereConditions.effective_date = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    whereConditions.effective_date = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    whereConditions.effective_date = {
      [Op.lte]: new Date(endDate)
    };
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await ExchangeRate.findAndCountAll({
    where: whereConditions,
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    exchangeRates: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Güncel döviz kurlarını getirme servisi
 * @param {string} baseCurrency - Baz para birimi
 * @returns {Object} Güncel döviz kurları
 */
exports.getCurrentRates = async (baseCurrency = 'EUR') => {
  // Bugünün tarihi
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Son 7 gün içindeki en güncel kurları getir
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  // Veritabanı sorgusu
  const rates = await ExchangeRate.findAll({
    where: {
      from_currency: baseCurrency,
      effective_date: {
        [Op.between]: [sevenDaysAgo, today]
      }
    },
    order: [['effective_date', 'DESC']]
  });
  
  // Para birimleri bazında en güncel kurları grupla
  const latestRates = {};
  
  rates.forEach(rate => {
    if (!latestRates[rate.to_currency] || new Date(rate.effective_date) > new Date(latestRates[rate.to_currency].effective_date)) {
      latestRates[rate.to_currency] = rate;
    }
  });
  
  // Sonuç formatını düzenle
  const result = {
    baseCurrency,
    date: today,
    rates: {}
  };
  
  Object.values(latestRates).forEach(rate => {
    result.rates[rate.to_currency] = {
      currency: rate.to_currency,
      rate: parseFloat(rate.rate),
      effectiveDate: rate.effective_date
    };
  });
  
  return result;
};

/**
 * İki para birimi arasındaki kuru getirme servisi
 * @param {string} from - Kaynak para birimi
 * @param {string} to - Hedef para birimi
 * @param {string} date - Tarih (opsiyonel)
 * @returns {Object} Döviz kuru
 */
exports.getRate = async (from, to, date) => {
  // Tarih kontrolü
  const targetDate = date ? new Date(date) : new Date();
  targetDate.setHours(0, 0, 0, 0);
  
  // Aynı para birimi ise
  if (from === to) {
    return {
      fromCurrency: from,
      toCurrency: to,
      rate: 1,
      effectiveDate: targetDate
    };
  }
  
  // Doğrudan kur var mı kontrol et
  let rate = await ExchangeRate.findOne({
    where: {
      from_currency: from,
      to_currency: to,
      effective_date: {
        [Op.lte]: targetDate
      }
    },
    order: [['effective_date', 'DESC']]
  });
  
  if (rate) {
    return {
      fromCurrency: from,
      toCurrency: to,
      rate: parseFloat(rate.rate),
      effectiveDate: rate.effective_date
    };
  }
  
  // Ters kur var mı kontrol et
  rate = await ExchangeRate.findOne({
    where: {
      from_currency: to,
      to_currency: from,
      effective_date: {
        [Op.lte]: targetDate
      }
    },
    order: [['effective_date', 'DESC']]
  });
  
  if (rate) {
    return {
      fromCurrency: from,
      toCurrency: to,
      rate: 1 / parseFloat(rate.rate),
      effectiveDate: rate.effective_date
    };
  }
  
  // EUR üzerinden çapraz kur hesapla
  if (from !== 'EUR' && to !== 'EUR') {
    const fromEurRate = await ExchangeRate.findOne({
      where: {
        from_currency: 'EUR',
        to_currency: from,
        effective_date: {
          [Op.lte]: targetDate
        }
      },
      order: [['effective_date', 'DESC']]
    });
    
    const toEurRate = await ExchangeRate.findOne({
      where: {
        from_currency: 'EUR',
        to_currency: to,
        effective_date: {
          [Op.lte]: targetDate
        }
      },
      order: [['effective_date', 'DESC']]
    });
    
    if (fromEurRate && toEurRate) {
      const crossRate = parseFloat(toEurRate.rate) / parseFloat(fromEurRate.rate);
      
      return {
        fromCurrency: from,
        toCurrency: to,
        rate: crossRate,
        effectiveDate: new Date(Math.min(fromEurRate.effective_date, toEurRate.effective_date)),
        isCrossRate: true
      };
    }
  }
  
  throw new AppError(`${from} ve ${to} para birimleri arasında kur bulunamadı`, 404);
};

/**
 * Yeni döviz kuru oluşturma servisi
 * @param {Object} rateData - Döviz kuru verileri
 * @returns {Object} Oluşturulan döviz kuru
 */
exports.createExchangeRate = async (rateData) => {
  const { fromCurrency, toCurrency, rate, effectiveDate } = rateData;

  // Aynı para birimi kontrolü
  if (fromCurrency === toCurrency) {
    throw new AppError('Kaynak ve hedef para birimi aynı olamaz', 400);
  }

  // Aynı tarih için kur var mı kontrol et
  const existingRate = await ExchangeRate.findOne({
    where: {
      from_currency: fromCurrency,
      to_currency: toCurrency,
      effective_date: effectiveDate
    }
  });

  if (existingRate) {
    throw new AppError('Bu tarih için kur zaten mevcut', 400);
  }

  // Yeni kur oluştur
  const newRate = await ExchangeRate.create({
    from_currency: fromCurrency,
    to_currency: toCurrency,
    rate,
    effective_date: effectiveDate
  });

  return newRate;
};

/**
 * Döviz kuru güncelleme servisi
 * @param {number} id - Döviz kuru ID
 * @param {Object} rateData - Güncellenecek veriler
 * @returns {Object} Güncellenen döviz kuru
 */
exports.updateExchangeRate = async (id, rateData) => {
  const { rate } = rateData;

  // Kuru bul
  const exchangeRate = await ExchangeRate.findByPk(id);
  
  if (!exchangeRate) {
    throw new AppError('Döviz kuru bulunamadı', 404);
  }

  // Güncelle
  await exchangeRate.update({
    rate: rate !== undefined ? rate : exchangeRate.rate
  });

  return exchangeRate;
};

/**
 * Döviz kuru silme servisi
 * @param {number} id - Döviz kuru ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteExchangeRate = async (id) => {
  // Kuru bul
  const exchangeRate = await ExchangeRate.findByPk(id);
  
  if (!exchangeRate) {
    throw new AppError('Döviz kuru bulunamadı', 404);
  }

  // Sil
  await exchangeRate.destroy();
  
  return true;
};

/**
 * Döviz kurlarını dış API'den senkronize etme servisi
 * @param {string} baseCurrency - Baz para birimi
 * @returns {Object} Senkronizasyon sonucu
 */
exports.syncExchangeRates = async (baseCurrency = 'EUR') => {
  try {
    // Dış API'den kurları al (örnek: ExchangeRate API)
    const response = await axios.get(`https://api.exchangerate.host/latest?base=${baseCurrency}`);
    
    if (!response.data || !response.data.rates) {
      throw new AppError('Döviz kurları alınamadı', 500);
    }
    
    const { rates, date } = response.data;
    const effectiveDate = new Date(date);
    
    // İşlem başlat
    const transaction = await sequelize.transaction();
    
    try {
      const results = {
        created: 0,
        updated: 0,
        skipped: 0
      };
      
      // Her para birimi için kur oluştur/güncelle
      for (const [currency, rate] of Object.entries(rates)) {
        // Aynı tarih için kur var mı kontrol et
        const existingRate = await ExchangeRate.findOne({
          where: {
            from_currency: baseCurrency,
            to_currency: currency,
            effective_date: effectiveDate
          },
          transaction
        });
        
        if (existingRate) {
          // Kur değiştiyse güncelle
          if (parseFloat(existingRate.rate) !== rate) {
            await existingRate.update({
              rate
            }, { transaction });
            
            results.updated++;
          } else {
            results.skipped++;
          }
        } else {
          // Yeni kur oluştur
          await ExchangeRate.create({
            from_currency: baseCurrency,
            to_currency: currency,
            rate,
            effective_date: effectiveDate
          }, { transaction });
          
          results.created++;
        }
      }
      
      await transaction.commit();
      
      return {
        baseCurrency,
        date: effectiveDate,
        results
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Döviz kurları senkronize edilemedi: ' + error.message, 500);
  }
};

/**
 * Para birimi dönüşümü yapma servisi
 * @param {number} amount - Miktar
 * @param {string} fromCurrency - Kaynak para birimi
 * @param {string} toCurrency - Hedef para birimi
 * @param {string} date - Tarih (opsiyonel)
 * @returns {Object} Dönüşüm sonucu
 */
exports.convertCurrency = async (amount, fromCurrency, toCurrency, date) => {
  // Kur bilgisini al
  const rateInfo = await this.getRate(fromCurrency, toCurrency, date);
  
  // Dönüşümü yap
  const convertedAmount = parseFloat(amount) * rateInfo.rate;
  
  return {
    originalAmount: parseFloat(amount),
    originalCurrency: fromCurrency,
    convertedAmount,
    convertedCurrency: toCurrency,
    rate: rateInfo.rate,
    effectiveDate: rateInfo.effectiveDate,
    isCrossRate: rateInfo.isCrossRate || false
  };
};
