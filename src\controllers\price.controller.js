// server/controllers/price.controller.js

const priceService = require('../services/price.service');

/**
 * Tüm fiyatları getir
 * @route GET /api/prices
 */
exports.getAllPrices = async (req, res, next) => {
  try {
    const {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page = 1,
      limit = 20,
      sortBy = 'id',
      sortDir = 'ASC'
    } = req.query;

    // Sayfa ve limit değerlerini sayıya dönüştür
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;

    const filters = {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page: pageNum,
      limit: limitNum,
      sortBy,
      sortDir
    };

    const result = await priceService.getAllPrices(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * <PERSON><PERSON><PERSON> bir acenteye ait fiyatları getir
 * @route GET /api/prices/agency/:agencyId
 */
exports.getPricesByAgency = async (req, res, next) => {
  try {
    const { agencyId } = req.params;
    const {
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page = 1,
      limit = 20
    } = req.query;

    // Sayfa ve limit değerlerini sayıya dönüştür
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;

    const filters = {
      agencyId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page: pageNum,
      limit: limitNum
    };

    const result = await priceService.getPricesByAgency(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belirli bir taşıyıcıya ait fiyatları getir
 * @route GET /api/prices/carrier/:carrierId
 */
exports.getPricesByCarrier = async (req, res, next) => {
  try {
    const { carrierId } = req.params;
    const {
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page = 1,
      limit = 20
    } = req.query;

    // Sayfa ve limit değerlerini sayıya dönüştür
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;

    const filters = {
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      page: pageNum,
      limit: limitNum
    };

    const result = await priceService.getPricesByCarrier(filters);

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Belirli bir fiyatı ID'ye göre getir
 * @route GET /api/prices/:id
 */
exports.getPriceById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const price = await priceService.getPriceById(id);

    res.status(200).json({
      success: true,
      data: price
    });
  } catch (error) {
    next(error);
  }
};

/**
 * İki bölge arasındaki fiyatı hesapla
 * @route GET /api/prices/calculate
 */
exports.calculatePrice = async (req, res, next) => {
  try {
    const {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      date
    } = req.query;

    const price = await priceService.calculatePrice(
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      date
    );

    res.status(200).json({
      success: true,
      data: price
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Yeni fiyat oluştur
 * @route POST /api/prices
 */
exports.createPrice = async (req, res, next) => {
  try {
    const {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      price,
      currency,
      validFrom,
      validTo
    } = req.body;

    const newPrice = await priceService.createPrice({
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      price,
      currency,
      validFrom,
      validTo
    });

    res.status(201).json({
      success: true,
      data: newPrice,
      message: 'Fiyat başarıyla oluşturuldu'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Fiyat güncelle
 * @route PUT /api/prices/:id
 */
exports.updatePrice = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      price,
      currency,
      validFrom,
      validTo
    } = req.body;

    const updatedPrice = await priceService.updatePrice(id, {
      agencyId,
      carrierId,
      fromRegionId,
      toRegionId,
      vehicleTypeId,
      price,
      currency,
      validFrom,
      validTo
    });

    res.status(200).json({
      success: true,
      data: updatedPrice,
      message: 'Fiyat başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Fiyat sil
 * @route DELETE /api/prices/:id
 */
exports.deletePrice = async (req, res, next) => {
  try {
    const { id } = req.params;

    await priceService.deletePrice(id);

    res.status(200).json({
      success: true,
      message: 'Fiyat başarıyla silindi'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Toplu fiyat oluştur/güncelle
 * @route POST /api/prices/bulk
 */
exports.bulkUpdatePrices = async (req, res, next) => {
  try {
    const { prices } = req.body;

    const result = await priceService.bulkUpdatePrices(prices);

    res.status(200).json({
      success: true,
      data: result,
      message: 'Fiyatlar başarıyla güncellendi'
    });
  } catch (error) {
    next(error);
  }
};
