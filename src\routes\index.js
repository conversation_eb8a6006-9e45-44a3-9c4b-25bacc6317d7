// server/routes/index.js

const express = require('express');
const router = express.Router();

const authRoutes = require('./auth.routes');
const userRoutes = require('./user.routes');
const agencyRoutes = require('./agency.routes');
const regionRoutes = require('./region.routes');
const vehicleRoutes = require('./vehicle.routes');
const reservationRoutes = require('./reservation.routes');
const transferRoutes = require('./transfer.routes');

// API rotaları
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/agencies', agencyRoutes);
router.use('/regions', regionRoutes);
router.use('/vehicles', vehicleRoutes);
router.use('/reservations', reservationRoutes);
router.use('/transfers', transferRoutes);

module.exports = router;
