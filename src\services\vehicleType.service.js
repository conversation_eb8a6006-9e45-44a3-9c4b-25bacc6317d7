// server/services/vehicleType.service.js

const { VehicleType, Vehicle, sequelize } = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm araç tiplerini listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç tipleri listesi ve sayfalama bilgileri
 */
exports.getAllVehicleTypes = async (filters) => {
  const { 
    name, 
    capacity,
    page = 1, 
    limit = 20,
    sortBy = 'name',
    sortDir = 'ASC'
  } = filters;

  // Filtreleme koşulları
  const whereConditions = {};
  
  if (name) {
    whereConditions.name = { [Op.like]: `%${name}%` };
  }
  
  if (capacity) {
    whereConditions.capacity = capacity;
  }

  // Sayfalama
  const offset = (page - 1) * limit;
  
  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await VehicleType.findAndCountAll({
    where: whereConditions,
    order,
    limit,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    vehicleTypes: rows,
    pagination: {
      total: count,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Araç tipi detaylarını getirme servisi
 * @param {number} id - Araç tipi ID
 * @returns {Object} Araç tipi detayları
 */
exports.getVehicleTypeById = async (id) => {
  const vehicleType = await VehicleType.findByPk(id);
  
  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }
  
  return vehicleType;
};

/**
 * Yeni araç tipi oluşturma servisi
 * @param {Object} vehicleTypeData - Araç tipi verileri
 * @returns {Object} Oluşturulan araç tipi
 */
exports.createVehicleType = async (vehicleTypeData) => {
  const { name, capacity, description } = vehicleTypeData;

  // İsim kontrolü
  const existingVehicleType = await VehicleType.findOne({
    where: { name }
  });

  if (existingVehicleType) {
    throw new AppError('Bu isimde bir araç tipi zaten mevcut', 400);
  }

  // Yeni araç tipi oluştur
  const newVehicleType = await VehicleType.create({
    name,
    capacity,
    description
  });

  return newVehicleType;
};

/**
 * Araç tipi güncelleme servisi
 * @param {number} id - Araç tipi ID
 * @param {Object} vehicleTypeData - Güncellenecek veriler
 * @returns {Object} Güncellenen araç tipi
 */
exports.updateVehicleType = async (id, vehicleTypeData) => {
  const { name, capacity, description } = vehicleTypeData;

  // Araç tipini bul
  const vehicleType = await VehicleType.findByPk(id);
  
  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }

  // İsim kontrolü (eğer isim değiştiyse)
  if (name && name !== vehicleType.name) {
    const existingVehicleType = await VehicleType.findOne({
      where: { name }
    });

    if (existingVehicleType) {
      throw new AppError('Bu isimde bir araç tipi zaten mevcut', 400);
    }
  }

  // Güncelle
  await vehicleType.update({
    name: name || vehicleType.name,
    capacity: capacity || vehicleType.capacity,
    description: description !== undefined ? description : vehicleType.description
  });

  return vehicleType;
};

/**
 * Araç tipi silme servisi
 * @param {number} id - Araç tipi ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteVehicleType = async (id) => {
  // Araç tipini bul
  const vehicleType = await VehicleType.findByPk(id);
  
  if (!vehicleType) {
    throw new AppError('Araç tipi bulunamadı', 404);
  }

  // Bu araç tipine bağlı araçlar var mı kontrol et
  const vehicleCount = await Vehicle.count({
    where: { vehicle_type_id: id }
  });

  if (vehicleCount > 0) {
    throw new AppError('Bu araç tipine bağlı araçlar bulunduğu için silinemez', 400);
  }

  // Sil
  await vehicleType.destroy();
  
  return true;
};
