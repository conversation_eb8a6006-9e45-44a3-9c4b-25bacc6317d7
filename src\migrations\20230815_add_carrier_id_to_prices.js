'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('prices', 'carrier_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'carriers',
        key: 'id'
      }
    });

    // Unique index güncelleme
    await queryInterface.removeIndex('prices', 'prices_agency_id_from_region_id_to_region_id_vehicle_type_id_valid_from_unique');
    
    await queryInterface.addIndex('prices', 
      ['agency_id', 'carrier_id', 'from_region_id', 'to_region_id', 'vehicle_type_id', 'valid_from'], 
      {
        unique: true,
        name: 'prices_unique_index'
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Unique index geri alma
    await queryInterface.removeIndex('prices', 'prices_unique_index');
    
    await queryInterface.addIndex('prices', 
      ['agency_id', 'from_region_id', 'to_region_id', 'vehicle_type_id', 'valid_from'], 
      {
        unique: true,
        name: 'prices_agency_id_from_region_id_to_region_id_vehicle_type_id_valid_from_unique'
      }
    );

    await queryInterface.removeColumn('prices', 'carrier_id');
  }
};
