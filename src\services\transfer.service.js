// server/services/transfer.service.js

const {
  Transfer,
  Reservation,
  Vehicle,
  VehicleType,
  Agency,
  Carrier,
  Region,
  User,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm transferleri listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @param {Object} user - Kullanıcı bilgileri
 * @returns {Object} Transfer listesi ve sayfalama bilgileri
 */
exports.getAllTransfers = async (filters, user) => {
  const {
    status,
    date,
    vehicleId,
    page = 1,
    limit = 20,
    sortBy = 'transfer_date',
    sortDir = 'ASC'
  } = filters;

  // Filtreleme koşulları
  const conditions = {};

  if (status) conditions.status = status;
  if (vehicleId) conditions.vehicle_id = vehicleId;

  // Tarih filtreleme
  if (date) {
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    conditions.transfer_date = {
      [Op.between]: [startDate, endDate]
    };
  }

  // Rol bazlı erişim kontrolü
  if (user.role === 'agent') {
    // Acente kullanıcıları sadece kendi acentelerinin transferlerini görebilir
    if (user.agency_id) {
      conditions['$reservation.agency_id$'] = user.agency_id;
    } else {
      throw new AppError('Bu işlem için yetkiniz yok', 403);
    }
  }

  // Sayfalama ve sıralama
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;
  const offset = (pageNum - 1) * limitNum;
  // Sıralama için sütun adını kontrol et
  let orderColumn = sortBy;
  if (sortBy === 'transferDate') {
    orderColumn = 'transfer_date';
  }
  const order = [[orderColumn, sortDir]];

  const { count, rows: transfers } = await Transfer.findAndCountAll({
    where: conditions,
    include: [
      {
        model: Reservation,
        as: 'reservation',
        include: [
          { model: Agency, as: 'agency' },
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' },
          { model: User, as: 'creator', attributes: ['id', 'username', 'full_name'] }
        ]
      },
      {
        model: Carrier,
        as: 'carrier'
      },
      {
        model: Vehicle,
        as: 'vehicle',
        include: [
          { model: VehicleType, as: 'vehicleType' }
        ]
      }
    ],
    order,
    limit: limitNum,
    offset
  });

  // Sayfalama meta verisi
  const totalPages = Math.ceil(count / limitNum);
  const hasNext = pageNum < totalPages;
  const hasPrevious = pageNum > 1;

  return {
    transfers,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNext,
      hasPrevious
    }
  };
};

/**
 * Transfer detayı getirme servisi
 * @param {number} id - Transfer ID
 * @param {Object} user - Kullanıcı bilgileri
 * @returns {Object} Transfer detayları
 */
exports.getTransferById = async (id, user) => {
  const transfer = await Transfer.findByPk(id, {
    include: [
      {
        model: Reservation,
        as: 'reservation',
        include: [
          { model: Agency, as: 'agency' },
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' },
          { model: User, as: 'creator', attributes: ['id', 'username', 'full_name'] }
        ]
      },
      {
        model: Vehicle,
        as: 'vehicle',
        include: [
          { model: VehicleType, as: 'vehicleType' }
        ]
      }
    ]
  });

  if (!transfer) {
    throw new AppError('Transfer bulunamadı', 404);
  }

  // Acente kullanıcıları sadece kendi acentelerinin transferlerini görebilir
  if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
    throw new AppError('Bu transfere erişim izniniz yok', 403);
  }

  return transfer;
};

/**
 * Transfere taşıyıcı firma atama servisi
 * @param {number} id - Transfer ID
 * @param {number} carrierId - Taşıyıcı firma ID
 * @param {number} userId - Kullanıcı ID
 * @param {string} ipAddress - IP adresi
 * @returns {Object} Güncellenmiş transfer
 */
exports.assignCarrier = async (id, carrierId, userId, ipAddress) => {
  const transaction = await sequelize.transaction();

  try {
    // Transferi kontrol et
    const transfer = await Transfer.findByPk(id, {
      include: [
        { model: Reservation, as: 'reservation' }
      ]
    });

    if (!transfer) {
      throw new AppError('Transfer bulunamadı', 404);
    }

    // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
    const user = await User.findByPk(userId);
    if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
      throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
    }

    // Taşıyıcı firmayı kontrol et
    if (carrierId) {
      const carrier = await Carrier.findByPk(carrierId);
      if (!carrier) {
        throw new AppError('Taşıyıcı firma bulunamadı', 404);
      }
    }

    // Transferi güncelle
    await transfer.update(
      {
        carrier_id: carrierId || null,
        // Taşıyıcı firma atandığında durum otomatik olarak hazır olarak değişsin
        status: carrierId ? 'ready' : transfer.status
      },
      { transaction }
    );

    await transaction.commit();

    // Güncellenmiş transferi getir
    return getTransferById(id, user);
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Transfere araç atama servisi
 * @param {number} id - Transfer ID
 * @param {number} vehicleId - Araç ID
 * @param {number} userId - Kullanıcı ID
 * @param {string} ipAddress - IP adresi
 * @returns {Object} Güncellenmiş transfer
 */
exports.assignVehicle = async (id, vehicleId, userId, ipAddress) => {
  const transaction = await sequelize.transaction();

  try {
    // Transferi kontrol et
    const transfer = await Transfer.findByPk(id, {
      include: [
        { model: Reservation, as: 'reservation' }
      ]
    });

    if (!transfer) {
      throw new AppError('Transfer bulunamadı', 404);
    }

    // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
    const user = await User.findByPk(userId);
    if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
      throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
    }

    // Aracı kontrol et
    if (vehicleId) {
      const vehicle = await Vehicle.findByPk(vehicleId);
      if (!vehicle) {
        throw new AppError('Araç bulunamadı', 404);
      }
    }

    // Transferi güncelle
    await transfer.update(
      {
        vehicle_id: vehicleId || null,
        // Araç atandığında durum otomatik olarak onaylandı olarak değişsin
        status: vehicleId ? 'ready' : transfer.status
      },
      { transaction }
    );

    await transaction.commit();

    // Güncellenmiş transferi getir
    return getTransferById(id, user);
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Transfer durumu güncelleme servisi
 * @param {number} id - Transfer ID
 * @param {string} status - Yeni durum
 * @param {number} userId - Kullanıcı ID
 * @param {string} ipAddress - IP adresi
 * @returns {Object} Güncellenmiş transfer
 */
exports.updateStatus = async (id, status, userId, ipAddress) => {
  const transaction = await sequelize.transaction();

  try {
    // Durum kontrolü
    const validStatuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'];
    if (!validStatuses.includes(status)) {
      throw new AppError('Geçersiz durum değeri', 400);
    }

    // Transferi kontrol et
    const transfer = await Transfer.findByPk(id, {
      include: [
        { model: Reservation, as: 'reservation' }
      ]
    });

    if (!transfer) {
      throw new AppError('Transfer bulunamadı', 404);
    }

    // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
    const user = await User.findByPk(userId);
    if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
      throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
    }

    // Transferi güncelle
    await transfer.update({ status }, { transaction });

    // Rezervasyon durumunu da güncelle
    const reservation = await Reservation.findByPk(transfer.reservation_id);

    // Tüm transferler tamamlandıysa rezervasyon da tamamlandı olarak işaretlensin
    if (status === 'completed') {
      const allTransfers = await Transfer.findAll({
        where: { reservation_id: transfer.reservation_id }
      });

      const allCompleted = allTransfers.every(t => t.id === transfer.id || t.status === 'completed');

      if (allCompleted) {
        await reservation.update({ status: 'completed' }, { transaction });
      }
    }

    // Herhangi bir transfer iptal edildiyse rezervasyon da iptal edilsin
    if (status === 'cancelled' && reservation.status !== 'cancelled') {
      await reservation.update({ status: 'cancelled' }, { transaction });
    }

    await transaction.commit();

    // Güncellenmiş transferi getir
    return getTransferById(id, user);
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Transfer güncelleme servisi
 * @param {number} id - Transfer ID
 * @param {Object} updateData - Güncelleme verileri
 * @param {number} userId - Kullanıcı ID
 * @param {string} ipAddress - IP adresi
 * @returns {Object} Güncellenmiş transfer
 */
exports.updateTransfer = async (id, updateData, userId, ipAddress) => {
  const transaction = await sequelize.transaction();

  try {
    // Transferi kontrol et
    const transfer = await Transfer.findByPk(id, {
      include: [
        { model: Reservation, as: 'reservation' }
      ]
    });

    if (!transfer) {
      throw new AppError('Transfer bulunamadı', 404);
    }

    // Acente kullanıcıları sadece kendi acentelerinin transferlerini güncelleyebilir
    const user = await User.findByPk(userId);
    if (user.role === 'agent' && user.agency_id !== transfer.reservation.agency_id) {
      throw new AppError('Bu transferi güncelleme yetkiniz yok', 403);
    }

    // Güncellenecek alanları hazırla
    const fieldsToUpdate = {};
    if (updateData.transferDate !== undefined) fieldsToUpdate.transfer_date = updateData.transferDate;
    if (updateData.transferTime !== undefined) fieldsToUpdate.transfer_time = updateData.transferTime;
    if (updateData.flightNumber !== undefined) fieldsToUpdate.flight_number = updateData.flightNumber;
    if (updateData.pickupLocation !== undefined) fieldsToUpdate.pickup_location = updateData.pickupLocation;
    if (updateData.dropoffLocation !== undefined) fieldsToUpdate.dropoff_location = updateData.dropoffLocation;
    if (updateData.notes !== undefined) fieldsToUpdate.notes = updateData.notes;
    if (updateData.vehicleId !== undefined) fieldsToUpdate.vehicle_id = updateData.vehicleId;
    if (updateData.status !== undefined) fieldsToUpdate.status = updateData.status;

    // Transferi güncelle
    await transfer.update(fieldsToUpdate, { transaction });

    await transaction.commit();

    // Güncellenmiş transferi getir
    return getTransferById(id, user);
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

// Yardımcı fonksiyon
async function getTransferById(id, user) {
  const transfer = await Transfer.findByPk(id, {
    include: [
      {
        model: Reservation,
        as: 'reservation',
        include: [
          { model: Agency, as: 'agency' },
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' }
        ]
      },
      {
        model: Carrier,
        as: 'carrier'
      },
      {
        model: Vehicle,
        as: 'vehicle',
        include: [
          { model: VehicleType, as: 'vehicleType' }
        ]
      }
    ]
  });

  if (!transfer) {
    throw new AppError('Transfer bulunamadı', 404);
  }

  return transfer;
}
