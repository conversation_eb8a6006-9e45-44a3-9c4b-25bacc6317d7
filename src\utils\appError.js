// server/utils/appError.js

/**
 * <PERSON><PERSON> hata sınıfı
 * @class AppError
 * @extends Error
 */
class AppError extends Error {
  /**
   * @param {string} message - <PERSON><PERSON> mesaj<PERSON>
   * @param {number} statusCode - HTTP durum kodu
   */
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

module.exports = AppError;
