// server/services/report.service.js

const {
  Reservation,
  Transfer,
  Agency,
  Carrier,
  Vehicle,
  VehicleType,
  Region,
  // Hotel kaldırıldı
  User,
  Transaction,
  Account,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const { formatDate, formatCurrency } = require('../utils/helpers');

/**
 * Rezervasyon raporu servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Rezervasyon raporu
 */
exports.getReservationReport = async (filters) => {
  const {
    startDate,
    endDate,
    agencyId,
    status,
    vehicleTypeId,
    fromRegionId,
    toRegionId
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Filtreleme koşulları
  const whereConditions = {
    created_at: {
      [Op.between]: [startDateTime, endDateTime]
    }
  };

  if (agencyId) {
    whereConditions.agency_id = agencyId;
  }

  if (status) {
    whereConditions.status = status;
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  if (fromRegionId) {
    whereConditions.from_region_id = fromRegionId;
  }

  if (toRegionId) {
    whereConditions.to_region_id = toRegionId;
  }

  // Rezervasyonları getir
  const reservations = await Reservation.findAll({
    where: whereConditions,
    include: [
      { model: Agency, as: 'Agency' },
      { model: Region, as: 'fromRegion' },
      { model: Region, as: 'toRegion' },
      { model: VehicleType, as: 'VehicleType' },
      { model: User, as: 'creator' },
      {
        model: Transfer,
        as: 'Transfers',
        include: [
          { model: Vehicle, as: 'Vehicle' }
        ]
      }
    ],
    order: [['created_at', 'DESC']]
  });

  // İstatistikler
  const totalReservations = reservations.length;
  const totalPassengers = reservations.reduce((sum, res) => sum + res.passenger_count, 0);
  const totalRevenue = reservations.reduce((sum, res) => sum + parseFloat(res.price), 0);

  // Durum dağılımı
  const statusDistribution = {};
  reservations.forEach(res => {
    statusDistribution[res.status] = (statusDistribution[res.status] || 0) + 1;
  });

  // Acente dağılımı
  const agencyDistribution = {};
  reservations.forEach(res => {
    const agencyName = res.Agency ? res.Agency.name : 'Bilinmeyen';
    agencyDistribution[agencyName] = (agencyDistribution[agencyName] || 0) + 1;
  });

  // Araç tipi dağılımı
  const vehicleTypeDistribution = {};
  reservations.forEach(res => {
    const typeName = res.VehicleType ? res.VehicleType.name : 'Bilinmeyen';
    vehicleTypeDistribution[typeName] = (vehicleTypeDistribution[typeName] || 0) + 1;
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    summary: {
      totalReservations,
      totalPassengers,
      totalRevenue,
      averagePassengersPerReservation: totalReservations ? (totalPassengers / totalReservations).toFixed(2) : 0,
      averageRevenuePerReservation: totalReservations ? (totalRevenue / totalReservations).toFixed(2) : 0
    },
    distributions: {
      status: statusDistribution,
      agency: agencyDistribution,
      vehicleType: vehicleTypeDistribution
    },
    reservations: reservations.map(res => ({
      id: res.id,
      reservationNumber: res.reservation_number,
      agencyName: res.Agency ? res.Agency.name : 'Bilinmeyen',
      fromRegion: res.fromRegion ? res.fromRegion.name : 'Bilinmeyen',
      toRegion: res.toRegion ? res.toRegion.name : 'Bilinmeyen',
      vehicleType: res.VehicleType ? res.VehicleType.name : 'Bilinmeyen',
      passengerCount: res.passenger_count,
      price: res.price,
      currency: res.currency,
      status: res.status,
      createdAt: formatDate(res.created_at),
      createdBy: res.creator ? res.creator.full_name : 'Bilinmeyen'
    }))
  };
};

/**
 * Operasyon raporu servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Operasyon raporu
 */
exports.getOperationReport = async (filters) => {
  const {
    startDate,
    endDate,
    carrierId,
    status,
    vehicleTypeId,
    fromRegionId,
    toRegionId
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Filtreleme koşulları
  const whereConditions = {
    transfer_date: {
      [Op.between]: [startDateTime, endDateTime]
    }
  };

  if (status) {
    whereConditions.status = status;
  }

  // Transfer sorgusu
  const transfers = await Transfer.findAll({
    where: whereConditions,
    include: [
      {
        model: Reservation,
        as: 'Reservation',
        include: [
          { model: Agency, as: 'Agency' },
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' },
          { model: VehicleType, as: 'VehicleType' }
        ],
        where: {
          ...(vehicleTypeId && { vehicle_type_id: vehicleTypeId }),
          ...(fromRegionId && { from_region_id: fromRegionId }),
          ...(toRegionId && { to_region_id: toRegionId })
        }
      },
      {
        model: Vehicle,
        as: 'Vehicle',
        include: [
          { model: Carrier, as: 'Carrier' },
          { model: VehicleType, as: 'VehicleType' }
        ],
        where: carrierId ? { carrier_id: carrierId } : {}
      }
    ],
    order: [['transfer_date', 'ASC'], ['transfer_time', 'ASC']]
  });

  // İstatistikler
  const totalTransfers = transfers.length;
  const completedTransfers = transfers.filter(t => t.status === 'completed').length;
  const cancelledTransfers = transfers.filter(t => ['cancelled', 'no_show', 'failed'].includes(t.status)).length;
  const pendingTransfers = transfers.filter(t => ['pending', 'ready', 'in_progress'].includes(t.status)).length;

  // Durum dağılımı
  const statusDistribution = {};
  transfers.forEach(t => {
    statusDistribution[t.status] = (statusDistribution[t.status] || 0) + 1;
  });

  // Taşımacı dağılımı
  const carrierDistribution = {};
  transfers.forEach(t => {
    if (t.Vehicle && t.Vehicle.Carrier) {
      const carrierName = t.Vehicle.Carrier.name;
      carrierDistribution[carrierName] = (carrierDistribution[carrierName] || 0) + 1;
    }
  });

  // Araç dağılımı
  const vehicleDistribution = {};
  transfers.forEach(t => {
    if (t.Vehicle) {
      const vehiclePlate = t.Vehicle.plate_number;
      vehicleDistribution[vehiclePlate] = (vehicleDistribution[vehiclePlate] || 0) + 1;
    }
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    summary: {
      totalTransfers,
      completedTransfers,
      cancelledTransfers,
      pendingTransfers,
      completionRate: totalTransfers ? ((completedTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%',
      cancellationRate: totalTransfers ? ((cancelledTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%'
    },
    distributions: {
      status: statusDistribution,
      carrier: carrierDistribution,
      vehicle: vehicleDistribution
    },
    transfers: transfers.map(t => ({
      id: t.id,
      reservationNumber: t.Reservation ? t.Reservation.reservation_number : 'Bilinmeyen',
      transferDate: formatDate(t.transfer_date),
      transferTime: t.transfer_time,
      isReturn: t.is_return,
      fromRegion: t.Reservation && t.Reservation.fromRegion ? t.Reservation.fromRegion.name : 'Bilinmeyen',
      toRegion: t.Reservation && t.Reservation.toRegion ? t.Reservation.toRegion.name : 'Bilinmeyen',
      vehicleType: t.Reservation && t.Reservation.VehicleType ? t.Reservation.VehicleType.name : 'Bilinmeyen',
      vehicle: t.Vehicle ? t.Vehicle.plate_number : 'Atanmadı',
      carrier: t.Vehicle && t.Vehicle.Carrier ? t.Vehicle.Carrier.name : 'Bilinmeyen',
      status: t.status,
      passengerCount: t.Reservation ? t.Reservation.passenger_count : 0,
      agencyName: t.Reservation && t.Reservation.Agency ? t.Reservation.Agency.name : 'Bilinmeyen'
    }))
  };
};

/**
 * Finansal rapor servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Finansal rapor
 */
exports.getFinancialReport = async (filters) => {
  const {
    startDate,
    endDate,
    entityType,
    entityId,
    transactionType,
    currency
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Filtreleme koşulları
  const whereConditions = {
    transaction_date: {
      [Op.between]: [startDateTime, endDateTime]
    }
  };

  if (transactionType) {
    whereConditions.transaction_type = transactionType;
  }

  if (currency) {
    whereConditions.currency = currency;
  }

  // Hesap filtreleme
  let accountWhere = {};
  if (entityType && entityId) {
    accountWhere = {
      entity_type: entityType,
      entity_id: entityId
    };
  }

  // İşlem sorgusu
  const transactions = await Transaction.findAll({
    where: whereConditions,
    include: [
      {
        model: Account,
        where: Object.keys(accountWhere).length > 0 ? accountWhere : undefined,
        include: [
          { model: Agency, as: 'Agency' },
          { model: Carrier, as: 'Carrier' }
        ]
      },
      { model: User, as: 'creator' },
      { model: Reservation, as: 'Reservation' }
    ],
    order: [['transaction_date', 'DESC']]
  });

  // İstatistikler
  const totalTransactions = transactions.length;
  const totalIncome = transactions
    .filter(t => t.transaction_type === 'payment' || t.transaction_type === 'refund')
    .reduce((sum, t) => sum + parseFloat(t.amount), 0);

  const totalExpense = transactions
    .filter(t => t.transaction_type === 'charge' || t.transaction_type === 'adjustment')
    .reduce((sum, t) => sum + parseFloat(t.amount), 0);

  const netBalance = totalIncome - totalExpense;

  // İşlem tipi dağılımı
  const typeDistribution = {};
  transactions.forEach(t => {
    typeDistribution[t.transaction_type] = (typeDistribution[t.transaction_type] || 0) + 1;
  });

  // Varlık dağılımı
  const entityDistribution = {};
  transactions.forEach(t => {
    if (t.Account) {
      const entityName = t.Account.entity_type === 'agency' && t.Account.Agency
        ? t.Account.Agency.name
        : t.Account.entity_type === 'carrier' && t.Account.Carrier
          ? t.Account.Carrier.name
          : 'Bilinmeyen';

      const key = `${t.Account.entity_type}-${entityName}`;
      entityDistribution[key] = (entityDistribution[key] || 0) + parseFloat(t.amount);
    }
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    summary: {
      totalTransactions,
      totalIncome: formatCurrency(totalIncome, currency || 'EUR'),
      totalExpense: formatCurrency(totalExpense, currency || 'EUR'),
      netBalance: formatCurrency(netBalance, currency || 'EUR')
    },
    distributions: {
      transactionType: typeDistribution,
      entity: entityDistribution
    },
    transactions: transactions.map(t => ({
      id: t.id,
      date: formatDate(t.transaction_date),
      type: t.transaction_type,
      amount: formatCurrency(t.amount, t.currency),
      currency: t.currency,
      description: t.description,
      reference: t.reference_number,
      reservationNumber: t.Reservation ? t.Reservation.reservation_number : null,
      entityType: t.Account ? t.Account.entity_type : null,
      entityName: t.Account
        ? t.Account.entity_type === 'agency' && t.Account.Agency
          ? t.Account.Agency.name
          : t.Account.entity_type === 'carrier' && t.Account.Carrier
            ? t.Account.Carrier.name
            : 'Bilinmeyen'
        : null,
      createdBy: t.creator ? t.creator.full_name : 'Sistem'
    }))
  };
};

/**
 * Acente performans raporu servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Acente raporu
 */
exports.getAgencyReport = async (filters) => {
  const {
    startDate,
    endDate,
    agencyId
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Acente filtreleme
  const agencyWhere = agencyId ? { id: agencyId } : {};

  // Acenteleri getir
  const agencies = await Agency.findAll({
    where: agencyWhere,
    include: [
      {
        model: Reservation,
        as: 'Reservations',
        where: {
          created_at: {
            [Op.between]: [startDateTime, endDateTime]
          }
        },
        required: false,
        include: [
          { model: Region, as: 'fromRegion' },
          { model: Region, as: 'toRegion' },
          { model: VehicleType, as: 'VehicleType' },
          {
            model: Transfer,
            as: 'Transfers',
            required: false
          }
        ]
      },
      {
        model: Account,
        as: 'Accounts',
        required: false
      }
    ],
    order: [['name', 'ASC']]
  });

  // Rapor verilerini hazırla
  const agencyReports = agencies.map(agency => {
    const reservations = agency.Reservations || [];

    // Rezervasyon istatistikleri
    const totalReservations = reservations.length;
    const totalPassengers = reservations.reduce((sum, res) => sum + res.passenger_count, 0);
    const totalRevenue = reservations.reduce((sum, res) => sum + parseFloat(res.price), 0);

    // Durum dağılımı
    const statusDistribution = {};
    reservations.forEach(res => {
      statusDistribution[res.status] = (statusDistribution[res.status] || 0) + 1;
    });

    // Transfer istatistikleri
    const transfers = reservations.flatMap(res => res.Transfers || []);
    const completedTransfers = transfers.filter(t => t.status === 'completed').length;
    const cancelledTransfers = transfers.filter(t => ['cancelled', 'no_show', 'failed'].includes(t.status)).length;

    // Hesap bilgileri
    const accounts = agency.Accounts || [];
    const accountBalances = {};
    accounts.forEach(acc => {
      accountBalances[acc.currency] = acc.balance;
    });

    return {
      id: agency.id,
      name: agency.name,
      email: agency.email,
      phone: agency.phone,
      contactPerson: agency.contact_person,
      isActive: agency.is_active,
      summary: {
        totalReservations,
        totalPassengers,
        totalRevenue: formatCurrency(totalRevenue, 'EUR'),
        averagePassengersPerReservation: totalReservations ? (totalPassengers / totalReservations).toFixed(2) : 0,
        averageRevenuePerReservation: totalReservations ? formatCurrency(totalRevenue / totalReservations, 'EUR') : formatCurrency(0, 'EUR'),
        completedTransferRate: transfers.length ? ((completedTransfers / transfers.length) * 100).toFixed(2) + '%' : '0%',
        cancellationRate: transfers.length ? ((cancelledTransfers / transfers.length) * 100).toFixed(2) + '%' : '0%'
      },
      distributions: {
        status: statusDistribution
      },
      accountBalances,
      reservations: reservations.map(res => ({
        id: res.id,
        reservationNumber: res.reservation_number,
        fromRegion: res.fromRegion ? res.fromRegion.name : 'Bilinmeyen',
        toRegion: res.toRegion ? res.toRegion.name : 'Bilinmeyen',
        vehicleType: res.VehicleType ? res.VehicleType.name : 'Bilinmeyen',
        passengerCount: res.passenger_count,
        price: formatCurrency(res.price, res.currency),
        status: res.status,
        createdAt: formatDate(res.created_at)
      }))
    };
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    agencies: agencyReports
  };
};

/**
 * Taşımacı performans raporu servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Taşımacı raporu
 */
exports.getCarrierReport = async (filters) => {
  const {
    startDate,
    endDate,
    carrierId
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Taşımacı filtreleme
  const carrierWhere = carrierId ? { id: carrierId } : {};

  // Taşımacıları getir
  const carriers = await Carrier.findAll({
    where: carrierWhere,
    include: [
      {
        model: Vehicle,
        as: 'Vehicles',
        required: false,
        include: [
          { model: VehicleType, as: 'VehicleType' },
          {
            model: Transfer,
            as: 'Transfers',
            where: {
              transfer_date: {
                [Op.between]: [startDateTime, endDateTime]
              }
            },
            required: false,
            include: [
              {
                model: Reservation,
                as: 'Reservation',
                include: [
                  { model: Agency, as: 'Agency' },
                  { model: Region, as: 'fromRegion' },
                  { model: Region, as: 'toRegion' }
                ]
              }
            ]
          }
        ]
      },
      {
        model: Account,
        as: 'Accounts',
        required: false
      }
    ],
    order: [['name', 'ASC']]
  });

  // Rapor verilerini hazırla
  const carrierReports = carriers.map(carrier => {
    const vehicles = carrier.Vehicles || [];

    // Tüm transferleri topla
    const allTransfers = [];
    vehicles.forEach(vehicle => {
      if (vehicle.Transfers && vehicle.Transfers.length > 0) {
        allTransfers.push(...vehicle.Transfers);
      }
    });

    // Transfer istatistikleri
    const totalTransfers = allTransfers.length;
    const completedTransfers = allTransfers.filter(t => t.status === 'completed').length;
    const cancelledTransfers = allTransfers.filter(t => ['cancelled', 'no_show', 'failed'].includes(t.status)).length;
    const pendingTransfers = allTransfers.filter(t => ['pending', 'ready', 'in_progress'].includes(t.status)).length;

    // Durum dağılımı
    const statusDistribution = {};
    allTransfers.forEach(t => {
      statusDistribution[t.status] = (statusDistribution[t.status] || 0) + 1;
    });

    // Araç dağılımı
    const vehicleDistribution = {};
    allTransfers.forEach(t => {
      const vehiclePlate = t.Vehicle ? t.Vehicle.plate_number : 'Bilinmeyen';
      vehicleDistribution[vehiclePlate] = (vehicleDistribution[vehiclePlate] || 0) + 1;
    });

    // Hesap bilgileri
    const accounts = carrier.Accounts || [];
    const accountBalances = {};
    accounts.forEach(acc => {
      accountBalances[acc.currency] = acc.balance;
    });

    return {
      id: carrier.id,
      name: carrier.name,
      email: carrier.email,
      phone: carrier.phone,
      contactPerson: carrier.contact_person,
      isActive: carrier.is_active,
      vehicleCount: vehicles.length,
      summary: {
        totalTransfers,
        completedTransfers,
        cancelledTransfers,
        pendingTransfers,
        completionRate: totalTransfers ? ((completedTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%',
        cancellationRate: totalTransfers ? ((cancelledTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%'
      },
      distributions: {
        status: statusDistribution,
        vehicle: vehicleDistribution
      },
      accountBalances,
      vehicles: vehicles.map(v => ({
        id: v.id,
        plateNumber: v.plate_number,
        vehicleType: v.VehicleType ? v.VehicleType.name : 'Bilinmeyen',
        driverName: v.driver_name,
        driverPhone: v.driver_phone,
        isActive: v.is_active,
        driverRating: v.driver_rating,
        transferCount: v.Transfers ? v.Transfers.length : 0
      })),
      transfers: allTransfers.map(t => ({
        id: t.id,
        transferDate: formatDate(t.transfer_date),
        transferTime: t.transfer_time,
        isReturn: t.is_return,
        status: t.status,
        vehiclePlate: t.Vehicle ? t.Vehicle.plate_number : 'Atanmadı',
        reservationNumber: t.Reservation ? t.Reservation.reservation_number : 'Bilinmeyen',
        agencyName: t.Reservation && t.Reservation.Agency ? t.Reservation.Agency.name : 'Bilinmeyen',
        fromRegion: t.Reservation && t.Reservation.fromRegion ? t.Reservation.fromRegion.name : 'Bilinmeyen',
        toRegion: t.Reservation && t.Reservation.toRegion ? t.Reservation.toRegion.name : 'Bilinmeyen'
      }))
    };
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    carriers: carrierReports
  };
};

/**
 * Araç kullanım raporu servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç raporu
 */
exports.getVehicleReport = async (filters) => {
  const {
    startDate,
    endDate,
    vehicleId,
    vehicleTypeId,
    carrierId
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Araç filtreleme
  const vehicleWhere = {};

  if (vehicleId) {
    vehicleWhere.id = vehicleId;
  }

  if (vehicleTypeId) {
    vehicleWhere.vehicle_type_id = vehicleTypeId;
  }

  if (carrierId) {
    vehicleWhere.carrier_id = carrierId;
  }

  // Araçları getir
  const vehicles = await Vehicle.findAll({
    where: vehicleWhere,
    include: [
      { model: VehicleType, as: 'VehicleType' },
      { model: Carrier, as: 'Carrier' },
      {
        model: Transfer,
        as: 'Transfers',
        where: {
          transfer_date: {
            [Op.between]: [startDateTime, endDateTime]
          }
        },
        required: false,
        include: [
          {
            model: Reservation,
            as: 'Reservation',
            include: [
              { model: Agency, as: 'Agency' },
              { model: Region, as: 'fromRegion' },
              { model: Region, as: 'toRegion' }
            ]
          }
        ]
      }
    ],
    order: [['plate_number', 'ASC']]
  });

  // Rapor verilerini hazırla
  const vehicleReports = vehicles.map(vehicle => {
    const transfers = vehicle.Transfers || [];

    // Transfer istatistikleri
    const totalTransfers = transfers.length;
    const completedTransfers = transfers.filter(t => t.status === 'completed').length;
    const cancelledTransfers = transfers.filter(t => ['cancelled', 'no_show', 'failed'].includes(t.status)).length;
    const pendingTransfers = transfers.filter(t => ['pending', 'ready', 'in_progress'].includes(t.status)).length;

    // Durum dağılımı
    const statusDistribution = {};
    transfers.forEach(t => {
      statusDistribution[t.status] = (statusDistribution[t.status] || 0) + 1;
    });

    // Rota dağılımı
    const routeDistribution = {};
    transfers.forEach(t => {
      if (t.Reservation && t.Reservation.fromRegion && t.Reservation.toRegion) {
        const route = `${t.Reservation.fromRegion.name} -> ${t.Reservation.toRegion.name}`;
        routeDistribution[route] = (routeDistribution[route] || 0) + 1;
      }
    });

    return {
      id: vehicle.id,
      plateNumber: vehicle.plate_number,
      vehicleType: vehicle.VehicleType ? vehicle.VehicleType.name : 'Bilinmeyen',
      carrier: vehicle.Carrier ? vehicle.Carrier.name : 'Bilinmeyen',
      driverName: vehicle.driver_name,
      driverPhone: vehicle.driver_phone,
      isActive: vehicle.is_active,
      driverRating: vehicle.driver_rating,
      summary: {
        totalTransfers,
        completedTransfers,
        cancelledTransfers,
        pendingTransfers,
        completionRate: totalTransfers ? ((completedTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%',
        cancellationRate: totalTransfers ? ((cancelledTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%'
      },
      distributions: {
        status: statusDistribution,
        route: routeDistribution
      },
      transfers: transfers.map(t => ({
        id: t.id,
        transferDate: formatDate(t.transfer_date),
        transferTime: t.transfer_time,
        isReturn: t.is_return,
        status: t.status,
        reservationNumber: t.Reservation ? t.Reservation.reservation_number : 'Bilinmeyen',
        agencyName: t.Reservation && t.Reservation.Agency ? t.Reservation.Agency.name : 'Bilinmeyen',
        fromRegion: t.Reservation && t.Reservation.fromRegion ? t.Reservation.fromRegion.name : 'Bilinmeyen',
        toRegion: t.Reservation && t.Reservation.toRegion ? t.Reservation.toRegion.name : 'Bilinmeyen'
      }))
    };
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    vehicles: vehicleReports
  };
};

/**
 * Bölge bazlı rapor servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Bölge raporu
 */
exports.getRegionReport = async (filters) => {
  const {
    startDate,
    endDate,
    regionId,
    category
  } = filters;

  // Tarih kontrolü
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
    throw new AppError('Geçersiz tarih formatı', 400);
  }

  // Bölge filtreleme
  const regionWhere = {};

  if (regionId) {
    regionWhere.id = regionId;
  }

  if (category) {
    regionWhere.category = category;
  }

  // Bölgeleri getir
  const regions = await Region.findAll({
    where: regionWhere,
    include: [
      { model: Region, as: 'parentRegion' },
      { model: Region, as: 'subRegions' },
      {
        model: Reservation,
        as: 'FromReservations',
        where: {
          created_at: {
            [Op.between]: [startDateTime, endDateTime]
          }
        },
        required: false,
        include: [
          { model: Agency, as: 'Agency' },
          { model: VehicleType, as: 'VehicleType' },
          { model: Region, as: 'toRegion' }
        ]
      },
      {
        model: Reservation,
        as: 'ToReservations',
        where: {
          created_at: {
            [Op.between]: [startDateTime, endDateTime]
          }
        },
        required: false,
        include: [
          { model: Agency, as: 'Agency' },
          { model: VehicleType, as: 'VehicleType' },
          { model: Region, as: 'fromRegion' }
        ]
      }
    ],
    order: [['name', 'ASC']]
  });

  // Rapor verilerini hazırla
  const regionReports = regions.map(region => {
    const fromReservations = region.FromReservations || [];
    const toReservations = region.ToReservations || [];
    const allReservations = [...fromReservations, ...toReservations];

    // Rezervasyon istatistikleri
    const totalReservations = allReservations.length;
    const totalFromReservations = fromReservations.length;
    const totalToReservations = toReservations.length;
    const totalPassengers = allReservations.reduce((sum, res) => sum + res.passenger_count, 0);

    // Durum dağılımı
    const statusDistribution = {};
    allReservations.forEach(res => {
      statusDistribution[res.status] = (statusDistribution[res.status] || 0) + 1;
    });

    // Acente dağılımı
    const agencyDistribution = {};
    allReservations.forEach(res => {
      if (res.Agency) {
        const agencyName = res.Agency.name;
        agencyDistribution[agencyName] = (agencyDistribution[agencyName] || 0) + 1;
      }
    });

    // Araç tipi dağılımı
    const vehicleTypeDistribution = {};
    allReservations.forEach(res => {
      if (res.VehicleType) {
        const typeName = res.VehicleType.name;
        vehicleTypeDistribution[typeName] = (vehicleTypeDistribution[typeName] || 0) + 1;
      }
    });

    // Rota dağılımı
    const routeDistribution = {};

    // Giden rotalar
    fromReservations.forEach(res => {
      if (res.toRegion) {
        const route = `${region.name} -> ${res.toRegion.name}`;
        routeDistribution[route] = (routeDistribution[route] || 0) + 1;
      }
    });

    // Gelen rotalar
    toReservations.forEach(res => {
      if (res.fromRegion) {
        const route = `${res.fromRegion.name} -> ${region.name}`;
        routeDistribution[route] = (routeDistribution[route] || 0) + 1;
      }
    });

    return {
      id: region.id,
      name: region.name,
      category: region.category,
      parentRegion: region.parentRegion ? region.parentRegion.name : null,
      childRegionCount: region.subRegions ? region.subRegions.length : 0,
      hotelCount: 0, // Artık hotels tablosu olmadığı için 0
      summary: {
        totalReservations,
        totalFromReservations,
        totalToReservations,
        totalPassengers,
        fromRatio: totalReservations ? ((totalFromReservations / totalReservations) * 100).toFixed(2) + '%' : '0%',
        toRatio: totalReservations ? ((totalToReservations / totalReservations) * 100).toFixed(2) + '%' : '0%'
      },
      distributions: {
        status: statusDistribution,
        agency: agencyDistribution,
        vehicleType: vehicleTypeDistribution,
        route: routeDistribution
      },
      hotels: [], // Artık hotels tablosu olmadığı için boş dizi
      fromReservations: fromReservations.map(res => ({
        id: res.id,
        reservationNumber: res.reservation_number,
        toRegion: res.toRegion ? res.toRegion.name : 'Bilinmeyen',
        agencyName: res.Agency ? res.Agency.name : 'Bilinmeyen',
        vehicleType: res.VehicleType ? res.VehicleType.name : 'Bilinmeyen',
        passengerCount: res.passenger_count,
        status: res.status,
        createdAt: formatDate(res.created_at)
      })),
      toReservations: toReservations.map(res => ({
        id: res.id,
        reservationNumber: res.reservation_number,
        fromRegion: res.fromRegion ? res.fromRegion.name : 'Bilinmeyen',
        agencyName: res.Agency ? res.Agency.name : 'Bilinmeyen',
        vehicleType: res.VehicleType ? res.VehicleType.name : 'Bilinmeyen',
        passengerCount: res.passenger_count,
        status: res.status,
        createdAt: formatDate(res.created_at)
      }))
    };
  });

  return {
    period: {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    },
    regions: regionReports
  };
};

/**
 * Dashboard özet raporu servisi
 * @param {string} period - Dönem (today, week, month, year)
 * @param {Object} user - Kullanıcı bilgileri
 * @returns {Object} Dashboard raporu
 */
exports.getDashboardReport = async (period, user) => {
  // Tarih aralığını belirle
  const endDate = new Date();
  let startDate;

  switch (period) {
    case 'today':
      startDate = new Date(endDate);
      startDate.setHours(0, 0, 0, 0);
      break;
    case 'week':
      startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'month':
      startDate = new Date(endDate);
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'year':
      startDate = new Date(endDate);
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate = new Date(endDate);
      startDate.setHours(0, 0, 0, 0);
  }

  // Rezervasyon istatistikleri
  const reservationStats = await Reservation.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('passenger_count')), 'passengerCount'],
      [sequelize.fn('SUM', sequelize.col('price')), 'totalRevenue']
    ],
    where: {
      created_at: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: ['status']
  });

  // Transfer istatistikleri
  const transferStats = await Transfer.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: {
      transfer_date: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: ['status']
  });

  // İşlem istatistikleri
  const transactionStats = await Transaction.findAll({
    attributes: [
      'transaction_type',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount']
    ],
    where: {
      transaction_date: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: ['transaction_type']
  });

  // Acente istatistikleri
  const agencyStats = await Reservation.findAll({
    attributes: [
      [sequelize.col('Agency.id'), 'agencyId'],
      [sequelize.col('Agency.name'), 'agencyName'],
      [sequelize.fn('COUNT', sequelize.col('Reservation.id')), 'count']
    ],
    include: [
      {
        model: Agency,
        as: 'Agency',
        attributes: []
      }
    ],
    where: {
      created_at: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: ['Agency.id', 'Agency.name'],
    order: [[sequelize.fn('COUNT', sequelize.col('Reservation.id')), 'DESC']],
    limit: 5
  });

  // Taşımacı istatistikleri
  const carrierStats = await Transfer.findAll({
    attributes: [
      [sequelize.col('Vehicle.Carrier.id'), 'carrierId'],
      [sequelize.col('Vehicle.Carrier.name'), 'carrierName'],
      [sequelize.fn('COUNT', sequelize.col('Transfer.id')), 'count']
    ],
    include: [
      {
        model: Vehicle,
        as: 'Vehicle',
        attributes: [],
        include: [
          {
            model: Carrier,
            as: 'Carrier',
            attributes: []
          }
        ]
      }
    ],
    where: {
      transfer_date: {
        [Op.between]: [startDate, endDate]
      },
      '$Vehicle.id$': { [Op.ne]: null }
    },
    group: ['Vehicle.Carrier.id', 'Vehicle.Carrier.name'],
    order: [[sequelize.fn('COUNT', sequelize.col('Transfer.id')), 'DESC']],
    limit: 5
  });

  // Rezervasyon durumlarını işle
  const reservationStatusCounts = {};
  let totalReservations = 0;
  let totalPassengers = 0;
  let totalRevenue = 0;

  reservationStats.forEach(stat => {
    reservationStatusCounts[stat.status] = parseInt(stat.get('count'));
    totalReservations += parseInt(stat.get('count'));
    totalPassengers += parseInt(stat.get('passengerCount') || 0);
    totalRevenue += parseFloat(stat.get('totalRevenue') || 0);
  });

  // Transfer durumlarını işle
  const transferStatusCounts = {};
  let totalTransfers = 0;
  let completedTransfers = 0;

  transferStats.forEach(stat => {
    transferStatusCounts[stat.status] = parseInt(stat.get('count'));
    totalTransfers += parseInt(stat.get('count'));
    if (stat.status === 'completed') {
      completedTransfers += parseInt(stat.get('count'));
    }
  });

  // İşlem tiplerini işle
  const transactionTypeCounts = {};
  const transactionAmounts = {};
  let totalIncome = 0;
  let totalExpense = 0;

  transactionStats.forEach(stat => {
    const type = stat.transaction_type;
    const count = parseInt(stat.get('count'));
    const amount = parseFloat(stat.get('totalAmount') || 0);

    transactionTypeCounts[type] = count;
    transactionAmounts[type] = amount;

    if (type === 'payment' || type === 'refund') {
      totalIncome += amount;
    } else if (type === 'charge' || type === 'adjustment') {
      totalExpense += amount;
    }
  });

  // Kullanıcı rolüne göre özel veriler
  let roleSpecificData = {};

  if (user.role === 'admin' || user.role === 'manager') {
    // Yönetici için tüm veriler
    roleSpecificData = {
      topAgencies: agencyStats.map(stat => ({
        id: stat.get('agencyId'),
        name: stat.get('agencyName'),
        reservationCount: parseInt(stat.get('count'))
      })),
      topCarriers: carrierStats.map(stat => ({
        id: stat.get('carrierId'),
        name: stat.get('carrierName'),
        transferCount: parseInt(stat.get('count'))
      }))
    };
  } else if (user.role === 'finance') {
    // Finans için finansal veriler
    roleSpecificData = {
      financialSummary: {
        totalIncome: formatCurrency(totalIncome, 'EUR'),
        totalExpense: formatCurrency(totalExpense, 'EUR'),
        netBalance: formatCurrency(totalIncome - totalExpense, 'EUR')
      },
      transactionCounts: transactionTypeCounts
    };
  } else if (user.role === 'operation') {
    // Operasyon için transfer verileri
    const upcomingTransfers = await Transfer.findAll({
      where: {
        transfer_date: {
          [Op.gte]: new Date()
        },
        status: {
          [Op.in]: ['pending', 'ready']
        }
      },
      include: [
        {
          model: Reservation,
          as: 'Reservation',
          include: [
            { model: Agency, as: 'Agency' },
            { model: Region, as: 'fromRegion' },
            { model: Region, as: 'toRegion' }
          ]
        },
        { model: Vehicle, as: 'Vehicle' }
      ],
      order: [['transfer_date', 'ASC'], ['transfer_time', 'ASC']],
      limit: 10
    });

    roleSpecificData = {
      upcomingTransfers: upcomingTransfers.map(t => ({
        id: t.id,
        transferDate: formatDate(t.transfer_date),
        transferTime: t.transfer_time,
        status: t.status,
        reservationNumber: t.Reservation ? t.Reservation.reservation_number : 'Bilinmeyen',
        fromRegion: t.Reservation && t.Reservation.fromRegion ? t.Reservation.fromRegion.name : 'Bilinmeyen',
        toRegion: t.Reservation && t.Reservation.toRegion ? t.Reservation.toRegion.name : 'Bilinmeyen',
        vehicle: t.Vehicle ? t.Vehicle.plate_number : 'Atanmadı'
      }))
    };
  }

  return {
    period: {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      periodType: period
    },
    reservations: {
      total: totalReservations,
      totalPassengers,
      totalRevenue: formatCurrency(totalRevenue, 'EUR'),
      statusCounts: reservationStatusCounts
    },
    transfers: {
      total: totalTransfers,
      completed: completedTransfers,
      completionRate: totalTransfers ? ((completedTransfers / totalTransfers) * 100).toFixed(2) + '%' : '0%',
      statusCounts: transferStatusCounts
    },
    transactions: {
      income: formatCurrency(totalIncome, 'EUR'),
      expense: formatCurrency(totalExpense, 'EUR'),
      netBalance: formatCurrency(totalIncome - totalExpense, 'EUR'),
      typeCounts: transactionTypeCounts
    },
    ...roleSpecificData
  };
};

/**
 * Rapor dışa aktarma servisi
 * @param {string} reportType - Rapor tipi
 * @param {string} format - Çıktı formatı (pdf/excel/json)
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Buffer|Object} Rapor dosyası veya JSON verisi
 */
exports.exportReport = async (reportType, format, filters) => {
  // Rapor verilerini al
  let reportData;

  switch (reportType) {
    case 'reservations':
      reportData = await this.getReservationReport(filters);
      break;
    case 'operations':
      reportData = await this.getOperationReport(filters);
      break;
    case 'financial':
      reportData = await this.getFinancialReport(filters);
      break;
    case 'agencies':
      reportData = await this.getAgencyReport(filters);
      break;
    case 'carriers':
      reportData = await this.getCarrierReport(filters);
      break;
    case 'vehicles':
      reportData = await this.getVehicleReport(filters);
      break;
    case 'regions':
      reportData = await this.getRegionReport(filters);
      break;
    default:
      throw new AppError('Geçersiz rapor tipi', 400);
  }

  // JSON formatı için direk veriyi döndür
  if (format === 'json') {
    return reportData;
  }

  // PDF formatı için
  if (format === 'pdf') {
    return generatePdfReport(reportType, reportData);
  }

  // Excel formatı için
  if (format === 'excel') {
    return generateExcelReport(reportType, reportData);
  }

  throw new AppError('Geçersiz format', 400);
};

/**
 * PDF rapor oluşturma yardımcı fonksiyonu
 * @param {string} reportType - Rapor tipi
 * @param {Object} data - Rapor verileri
 * @returns {Buffer} PDF dosyası
 */
const generatePdfReport = (reportType, data) => {
  const doc = new PDFDocument({ margin: 50 });
  const chunks = [];

  doc.on('data', chunk => chunks.push(chunk));

  // Başlık
  let title = '';
  switch (reportType) {
    case 'reservations': title = 'Rezervasyon Raporu'; break;
    case 'operations': title = 'Operasyon Raporu'; break;
    case 'financial': title = 'Finansal Rapor'; break;
    case 'agencies': title = 'Acente Performans Raporu'; break;
    case 'carriers': title = 'Taşımacı Performans Raporu'; break;
    case 'vehicles': title = 'Araç Kullanım Raporu'; break;
    case 'regions': title = 'Bölge Bazlı Rapor'; break;
  }

  doc.fontSize(20).text(title, { align: 'center' });
  doc.moveDown();

  // Dönem bilgisi
  doc.fontSize(12).text(`Dönem: ${data.period.startDate} - ${data.period.endDate}`, { align: 'center' });
  doc.moveDown(2);

  // Özet bilgiler
  doc.fontSize(16).text('Özet Bilgiler', { underline: true });
  doc.moveDown();

  // Rapor tipine göre özel içerik
  switch (reportType) {
    case 'reservations':
      doc.text(`Toplam Rezervasyon: ${data.summary.totalReservations}`);
      doc.text(`Toplam Yolcu: ${data.summary.totalPassengers}`);
      doc.text(`Toplam Gelir: ${data.summary.totalRevenue}`);
      break;
    case 'operations':
      doc.text(`Toplam Transfer: ${data.summary.totalTransfers}`);
      doc.text(`Tamamlanan Transfer: ${data.summary.completedTransfers}`);
      doc.text(`Tamamlanma Oranı: ${data.summary.completionRate}`);
      break;
    case 'financial':
      doc.text(`Toplam Gelir: ${data.summary.totalIncome}`);
      doc.text(`Toplam Gider: ${data.summary.totalExpense}`);
      doc.text(`Net Bakiye: ${data.summary.netBalance}`);
      break;
    // Diğer rapor tipleri için benzer özetler...
  }

  // Rapor tamamlandı
  doc.end();

  // Buffer olarak döndür
  return new Promise((resolve) => {
    doc.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
  });
};

/**
 * Excel rapor oluşturma yardımcı fonksiyonu
 * @param {string} reportType - Rapor tipi
 * @param {Object} data - Rapor verileri
 * @returns {Buffer} Excel dosyası
 */
const generateExcelReport = (reportType, data) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Rapor');

  // Başlık
  let title = '';
  switch (reportType) {
    case 'reservations': title = 'Rezervasyon Raporu'; break;
    case 'operations': title = 'Operasyon Raporu'; break;
    case 'financial': title = 'Finansal Rapor'; break;
    case 'agencies': title = 'Acente Performans Raporu'; break;
    case 'carriers': title = 'Taşımacı Performans Raporu'; break;
    case 'vehicles': title = 'Araç Kullanım Raporu'; break;
    case 'regions': title = 'Bölge Bazlı Rapor'; break;
  }

  worksheet.mergeCells('A1:H1');
  worksheet.getCell('A1').value = title;
  worksheet.getCell('A1').font = { size: 16, bold: true };
  worksheet.getCell('A1').alignment = { horizontal: 'center' };

  // Dönem bilgisi
  worksheet.mergeCells('A2:H2');
  worksheet.getCell('A2').value = `Dönem: ${data.period.startDate} - ${data.period.endDate}`;
  worksheet.getCell('A2').alignment = { horizontal: 'center' };

  // Özet bilgiler
  worksheet.getCell('A4').value = 'Özet Bilgiler';
  worksheet.getCell('A4').font = { size: 14, bold: true };

  // Rapor tipine göre özel içerik
  let rowIndex = 5;

  switch (reportType) {
    case 'reservations':
      worksheet.getCell(`A${rowIndex}`).value = 'Toplam Rezervasyon';
      worksheet.getCell(`B${rowIndex}`).value = data.summary.totalReservations;
      rowIndex++;
      worksheet.getCell(`A${rowIndex}`).value = 'Toplam Yolcu';
      worksheet.getCell(`B${rowIndex}`).value = data.summary.totalPassengers;
      rowIndex++;
      worksheet.getCell(`A${rowIndex}`).value = 'Toplam Gelir';
      worksheet.getCell(`B${rowIndex}`).value = data.summary.totalRevenue;
      rowIndex += 2;

      // Rezervasyon listesi
      if (data.reservations && data.reservations.length > 0) {
        worksheet.getCell(`A${rowIndex}`).value = 'Rezervasyon Listesi';
        worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true };
        rowIndex++;

        // Başlıklar
        worksheet.getCell(`A${rowIndex}`).value = 'Rezervasyon No';
        worksheet.getCell(`B${rowIndex}`).value = 'Acente';
        worksheet.getCell(`C${rowIndex}`).value = 'Nereden';
        worksheet.getCell(`D${rowIndex}`).value = 'Nereye';
        worksheet.getCell(`E${rowIndex}`).value = 'Araç Tipi';
        worksheet.getCell(`F${rowIndex}`).value = 'Yolcu Sayısı';
        worksheet.getCell(`G${rowIndex}`).value = 'Fiyat';
        worksheet.getCell(`H${rowIndex}`).value = 'Durum';
        rowIndex++;

        // Veriler
        data.reservations.forEach(res => {
          worksheet.getCell(`A${rowIndex}`).value = res.reservationNumber;
          worksheet.getCell(`B${rowIndex}`).value = res.agencyName;
          worksheet.getCell(`C${rowIndex}`).value = res.fromRegion;
          worksheet.getCell(`D${rowIndex}`).value = res.toRegion;
          worksheet.getCell(`E${rowIndex}`).value = res.vehicleType;
          worksheet.getCell(`F${rowIndex}`).value = res.passengerCount;
          worksheet.getCell(`G${rowIndex}`).value = res.price;
          worksheet.getCell(`H${rowIndex}`).value = res.status;
          rowIndex++;
        });
      }
      break;
    // Diğer rapor tipleri için benzer içerikler...
  }

  // Excel dosyasını buffer olarak döndür
  return workbook.xlsx.writeBuffer();
};