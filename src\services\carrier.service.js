// server/services/carrier.service.js

const {
  Carrier,
  Vehicle,
  VehicleType,
  Account,
  sequelize
} = require('../models');
const { Op } = require('sequelize');
const AppError = require('../utils/appError');

/**
 * Tüm taşımacıları listeleme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Taşımacı listesi ve sayfalama bilgileri
 */
exports.getAllCarriers = async (filters) => {
  const {
    name,
    isActive,
    page = 1,
    limit = 20,
    sortBy = 'name',
    sortDir = 'ASC'
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {};

  if (name) {
    whereConditions.name = { [Op.like]: `%${name}%` };
  }

  if (isActive !== undefined && isActive !== '') {
    whereConditions.is_active = isActive === 'true';
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Sıralama
  const order = [[sortBy, sortDir]];

  // Veritabanı sorgusu
  const { count, rows } = await Carrier.findAndCountAll({
    where: whereConditions,
    order,
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};

/**
 * Taşımacı detaylarını getirme servisi
 * @param {number} id - Taşımacı ID
 * @returns {Object} Taşımacı detayları
 */
exports.getCarrierById = async (id) => {
  const carrier = await Carrier.findByPk(id);

  if (!carrier) {
    throw new AppError('Taşımacı bulunamadı', 404);
  }

  // Taşımacıya ait araç sayısını getir
  const vehicleCount = await Vehicle.count({
    where: { carrier_id: id }
  });

  // Hesap bilgilerini getir
  const account = await Account.findOne({
    where: {
      entity_type: 'carrier',
      entity_id: id
    }
  });

  // Sonuç
  return {
    ...carrier.toJSON(),
    vehicleCount,
    account: account ? account.toJSON() : null
  };
};

/**
 * Yeni taşımacı oluşturma servisi
 * @param {Object} carrierData - Taşımacı verileri
 * @returns {Object} Oluşturulan taşımacı
 */
exports.createCarrier = async (carrierData) => {
  const {
    name,
    email,
    phone,
    address,
    contactPerson,
    carrierCode,
    isActive = true
  } = carrierData;

  // Kod kontrolü
  const existingCarrier = await Carrier.findOne({
    where: { carrier_code: carrierCode }
  });

  if (existingCarrier) {
    throw new AppError('Bu kod ile bir taşımacı zaten mevcut', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();

  try {
    // Yeni taşımacı oluştur
    const newCarrier = await Carrier.create({
      name,
      email,
      phone,
      address,
      contact_person: contactPerson,
      carrier_code: carrierCode,
      is_active: isActive
    }, { transaction });

    // Hesap oluştur
    await Account.create({
      entity_type: 'carrier',
      entity_id: newCarrier.id,
      balance: 0,
      currency: 'EUR',
      last_updated: new Date()
    }, { transaction });

    await transaction.commit();
    return newCarrier;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Taşımacı güncelleme servisi
 * @param {number} id - Taşımacı ID
 * @param {Object} carrierData - Güncellenecek veriler
 * @returns {Object} Güncellenen taşımacı
 */
exports.updateCarrier = async (id, carrierData) => {
  const {
    name,
    email,
    phone,
    address,
    contactPerson,
    carrierCode,
    isActive
  } = carrierData;

  // Taşımacıyı bul
  const carrier = await Carrier.findByPk(id);

  if (!carrier) {
    throw new AppError('Taşımacı bulunamadı', 404);
  }

  // Kod kontrolü (eğer değiştiyse)
  if (carrierCode && carrierCode !== carrier.carrier_code) {
    const existingCarrier = await Carrier.findOne({
      where: {
        carrier_code: carrierCode,
        id: { [Op.ne]: id }
      }
    });

    if (existingCarrier) {
      throw new AppError('Bu kod ile bir taşımacı zaten mevcut', 400);
    }
  }

  // Güncelle
  await carrier.update({
    name: name || carrier.name,
    email: email || carrier.email,
    phone: phone || carrier.phone,
    address: address !== undefined ? address : carrier.address,
    contact_person: contactPerson || carrier.contact_person,
    carrier_code: carrierCode || carrier.carrier_code,
    is_active: isActive !== undefined ? isActive : carrier.is_active
  });

  return carrier;
};

/**
 * Taşımacı silme servisi
 * @param {number} id - Taşımacı ID
 * @returns {boolean} İşlem başarılı mı
 */
exports.deleteCarrier = async (id) => {
  // Taşımacıyı bul
  const carrier = await Carrier.findByPk(id);

  if (!carrier) {
    throw new AppError('Taşımacı bulunamadı', 404);
  }

  // Taşımacıya ait araçlar var mı kontrol et
  const vehicleCount = await Vehicle.count({
    where: { carrier_id: id }
  });

  if (vehicleCount > 0) {
    throw new AppError('Bu taşımacıya ait araçlar bulunduğu için silinemez', 400);
  }

  // İşlem başlat
  const transaction = await sequelize.transaction();

  try {
    // Hesabı sil
    await Account.destroy({
      where: {
        entity_type: 'carrier',
        entity_id: id
      },
      transaction
    });

    // Taşımacıyı sil
    await carrier.destroy({ transaction });

    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Taşımacıya ait araçları getirme servisi
 * @param {Object} filters - Filtreleme parametreleri
 * @returns {Object} Araç listesi ve sayfalama bilgileri
 */
exports.getCarrierVehicles = async (filters) => {
  const {
    carrierId,
    isActive,
    vehicleTypeId,
    page = 1,
    limit = 20
  } = filters;

  // Sayfa ve limit değerlerini sayıya dönüştür
  const pageNum = parseInt(page, 10) || 1;
  const limitNum = parseInt(limit, 10) || 20;

  // Filtreleme koşulları
  const whereConditions = {
    carrier_id: carrierId
  };

  if (isActive !== undefined && isActive !== '') {
    whereConditions.is_active = isActive === 'true';
  }

  if (vehicleTypeId) {
    whereConditions.vehicle_type_id = vehicleTypeId;
  }

  // Sayfalama
  const offset = (pageNum - 1) * limitNum;

  // Veritabanı sorgusu
  const { count, rows } = await Vehicle.findAndCountAll({
    where: whereConditions,
    include: [
      { model: VehicleType, as: 'vehicleType' }
    ],
    order: [['plate_number', 'ASC']],
    limit: limitNum,
    offset
  });

  // Sayfalama bilgileri
  const totalPages = Math.ceil(count / limitNum);
  const hasNextPage = pageNum < totalPages;
  const hasPrevPage = pageNum > 1;

  return {
    rows,
    count,
    pagination: {
      total: count,
      totalPages,
      currentPage: pageNum,
      limit: limitNum,
      hasNextPage,
      hasPrevPage
    }
  };
};
