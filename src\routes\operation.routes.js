// server/routes/operation.routes.js

const express = require('express');
const router = express.Router();
const operationController = require('../controllers/operation.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');

/**
 * @route GET /api/operations/daily
 * @desc Günlük operasyon listesi
 * @access Private
 */
router.get(
  '/daily', 
  authenticate, 
  operationController.getDailyOperations
);

/**
 * @route PATCH /api/operations/transfers/:id/status
 * @desc Transfer durumu güncelleme
 * @access Private
 */
router.patch(
  '/transfers/:id/status', 
  authenticate, 
  operationController.updateTransferStatus
);

/**
 * @route POST /api/operations/transfers/:id/assign-vehicle
 * @desc Araca sürücü/araç atama
 * @access Private
 */
router.post(
  '/transfers/:id/assign-vehicle', 
  authenticate, 
  authorize(['admin', 'manager', 'operation']), 
  operationController.assignVehicle
);

/**
 * @route GET /api/operations/check-flight
 * @desc Uçuş bilgilerini kontrol et
 * @access Private
 */
router.get(
  '/check-flight', 
  authenticate, 
  operationController.checkFlightStatus
);

/**
 * @route POST /api/operations/transfers/:id/send-message
 * @desc Transfer için WhatsApp mesajı gönder
 * @access Private
 */
router.post(
  '/transfers/:id/send-message', 
  authenticate, 
  operationController.sendWhatsAppMessage
);

/**
 * @route PUT /api/operations/transfers/:id
 * @desc Transfer detaylarını güncelleme
 * @access Private
 */
router.put(
  '/transfers/:id', 
  authenticate, 
  operationController.updateTransferDetails
);

/**
 * @route GET /api/operations/summary
 * @desc Bugünkü transferlerin özeti
 * @access Private
 */
router.get(
  '/summary', 
  authenticate, 
  operationController.getDailySummary
);

/**
 * @route GET /api/operations/available-vehicles
 * @desc Araç listesini getir (sürücü atama için)
 * @access Private
 */
router.get(
  '/available-vehicles', 
  authenticate, 
  operationController.getAvailableVehicles
);

module.exports = router;