// server/routes/price.routes.js

const express = require('express');
const router = express.Router();
const priceController = require('../controllers/price.controller');
const { authenticate, authorize, checkPermission } = require('../middleware/auth');

/**
 * @route GET /api/prices
 * @desc Tüm fiyatları getir
 * @access Private
 */
router.get(
  '/',
  authenticate,
  priceController.getAllPrices
);

/**
 * @route GET /api/prices/agency/:agencyId
 * @desc Belirli bir acenteye ait fiyatları getir
 * @access Private
 */
router.get(
  '/agency/:agencyId',
  authenticate,
  priceController.getPricesByAgency
);

/**
 * @route GET /api/prices/carrier/:carrierId
 * @desc Belirli bir taşıyıcıya ait fiyatları getir
 * @access Private
 */
router.get(
  '/carrier/:carrierId',
  authenticate,
  priceController.getPricesByCarrier
);

/**
 * @route GET /api/prices/:id
 * @desc Belirli bir fiyatı ID'ye göre getir
 * @access Private
 */
router.get(
  '/:id',
  authenticate,
  priceController.getPriceById
);

/**
 * @route GET /api/prices/calculate
 * @desc İki bölge arasındaki fiyatı hesapla
 * @access Private
 */
router.get(
  '/calculate',
  authenticate,
  priceController.calculatePrice
);

/**
 * @route POST /api/prices
 * @desc Yeni fiyat oluştur
 * @access Private
 */
router.post(
  '/',
  authenticate,
  checkPermission('can_manage_prices'),
  priceController.createPrice
);

/**
 * @route PUT /api/prices/:id
 * @desc Fiyat güncelle
 * @access Private
 */
router.put(
  '/:id',
  authenticate,
  checkPermission('can_manage_prices'),
  priceController.updatePrice
);

/**
 * @route DELETE /api/prices/:id
 * @desc Fiyat sil
 * @access Private
 */
router.delete(
  '/:id',
  authenticate,
  authorize(['admin', 'manager']),
  priceController.deletePrice
);

/**
 * @route POST /api/prices/bulk
 * @desc Toplu fiyat oluştur/güncelle
 * @access Private
 */
router.post(
  '/bulk',
  authenticate,
  authorize(['admin', 'manager']),
  priceController.bulkUpdatePrices
);

module.exports = router;
